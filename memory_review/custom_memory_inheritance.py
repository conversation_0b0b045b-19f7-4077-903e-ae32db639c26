from mem0 import Memory
from memory_core import ClientFactory, Config<PERSON>anager, IndexManager, SafeDeleteMixin
from openai import OpenAI
from opensearchpy import OpenSearch


class CustomMemory(Memory, SafeDeleteMixin):
    """继承自 mem0.Memory 的自定义内存管理器.

    直接继承 Memory 类，支持注入自定义客户端实例，
    比组合模式更加优雅和符合 OOP 原则。
    """

    def __init__(
        self,
        llm_client: OpenAI | None = None,
        embedding_client: OpenAI | None = None,
        opensearch_client: OpenSearch | None = None,
        collection_name: str = "mem0_memories",
    ):
        """初始化自定义Memory实例.

        Args:
            llm_client: 用于LLM推理的OpenAI客户端
            embedding_client: 用于生成embeddings的OpenAI客户端
            opensearch_client: OpenSearch客户端
            collection_name: OpenSearch索引名称
        """
        # 使用工厂类创建客户端
        self.custom_llm_client = llm_client or ClientFactory.create_llm_client()
        self.custom_embedding_client = (
            embedding_client or ClientFactory.create_embedding_client()
        )
        self.custom_opensearch_client = (
            opensearch_client or ClientFactory.create_opensearch_client()
        )
        self.collection_name = collection_name

        # 使用配置管理器创建配置
        config = ConfigManager.create_memory_config(
            self.custom_llm_client,
            self.custom_embedding_client,
            self.collection_name
        )

        super().__init__(config)
        self._inject_custom_clients()

    def _inject_custom_clients(self):
        """注入自定义客户端到Memory实例中."""
        if hasattr(self, "llm") and hasattr(self.llm, "client"):
            self.llm.client = self.custom_llm_client

        if hasattr(self, "embedding_model") and hasattr(self.embedding_model, "client"):
            self.embedding_model.client = self.custom_embedding_client

            # 清理可能导致维度问题的配置
            if hasattr(self.embedding_model, "config"):
                config = self.embedding_model.config
                # 清理维度相关参数
                for attr in ["embedding_dims", "output_dimensionality", "dimensions"]:
                    if hasattr(config, attr):
                        setattr(config, attr, None)

        if hasattr(self, "vector_store") and hasattr(self.vector_store, "client"):
            self.vector_store.client = self.custom_opensearch_client

            # 确保 vector store 使用正确的 embedding 维度
            if hasattr(self.vector_store, "embedding_model_dims"):
                # 获取实际的 embedding 维度
                try:
                    test_embedding = self.embedding_model.embed("test")
                    if test_embedding:
                        actual_dims = len(test_embedding)
                        self.vector_store.embedding_model_dims = actual_dims
                        print(f"   🔧 设置 vector store 维度为: {actual_dims}")

                        # 使用索引管理器确保索引正确
                        index_manager = IndexManager(self.vector_store, self.collection_name)
                        try:
                            index_manager.ensure_correct_index()
                        except Exception as e:
                            print(f"   ⚠️ 索引检查警告: {str(e)[:50]}...")
                except:
                    # 如果无法获取，使用默认值
                    self.vector_store.embedding_model_dims = 1024



if __name__ == "__main__":
    # 演示功能已移至 demo.py 模块
    from demo import demo_basic_functions
    demo_basic_functions()
