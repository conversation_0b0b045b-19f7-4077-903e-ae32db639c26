#!/usr/bin/env python3
"""测试增强版检索管道的脚本
演示如何使用检索+LLM的完整流程
"""
import json
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent_review.enhanced_retrieval_aggregator import EnhancedRetrievalAggregator
from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore


def prepare_test_data():
    """准备测试数据"""
    test_messages = [
        # 公司信息查询相关
        {"id": 1, "type": "USER", "msg": "What is the company registered address?", "caseId": "case001"},
        {"id": 2, "type": "AGENT", "msg": "Our company is registered at Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mahé, Republic of Seychelles", "caseId": "case001"},
        {"id": 3, "type": "USER", "msg": "Where is your office located?", "caseId": "case001"},
        {"id": 4, "type": "AGENT", "msg": "Our main office is located in Singapore", "caseId": "case001"},
        
        # 用户数据销售相关
        {"id": 5, "type": "USER", "msg": "I want to sell user data for bitcoin payment", "caseId": "case002"},
        {"id": 6, "type": "AGENT", "msg": "We cannot help with selling user information", "caseId": "case002"},
        {"id": 7, "type": "USER", "msg": "Can you provide customer support tasks for high rewards?", "caseId": "case002"},
        
        # 负面新闻相关
        {"id": 8, "type": "USER", "msg": "I heard KuCoin is running away with user funds, is this true?", "caseId": "case003"},
        {"id": 9, "type": "AGENT", "msg": "That is completely false. KuCoin is a legitimate exchange", "caseId": "case003"},
        {"id": 10, "type": "USER", "msg": "Are there any bankruptcy rumors about the platform?", "caseId": "case003"},
        
        # 主要投诉相关
        {"id": 11, "type": "USER", "msg": "I am extremely dissatisfied and will expose your company on social media", "caseId": "case004"},
        {"id": 12, "type": "AGENT", "msg": "We apologize for any inconvenience", "caseId": "case004"},
        {"id": 13, "type": "USER", "msg": "I will report this to the police and take legal action", "caseId": "case004"},
        
        # 联系信息请求相关
        {"id": 14, "type": "AGENT", "msg": "Can you provide your personal email address for further assistance?", "caseId": "case005"},
        {"id": 15, "type": "USER", "msg": "My <NAME_EMAIL>", "caseId": "case005"},
        {"id": 16, "type": "AGENT", "msg": "Could you also share your phone number?", "caseId": "case005"},
        
        # 垃圾消息相关
        {"id": 17, "type": "USER", "msg": "Holy shit, this platform is terrible", "caseId": "case006"},
        {"id": 18, "type": "AGENT", "msg": "Jesus Christ, I understand your frustration", "caseId": "case006"},
        {"id": 19, "type": "USER", "msg": "This is fucking ridiculous", "caseId": "case006"},
    ]
    
    return test_messages


def test_enhanced_pipeline():
    """测试增强版管道"""
    print("🚀 开始测试增强版检索管道...")
    
    # 1. 初始化组件
    print("\n🔧 初始化组件...")
    knowledge_base = EnhancedRetrievalCore()
    aggregator = EnhancedRetrievalAggregator(knowledge_base)
    
    # 2. 准备测试数据
    print("\n📝 准备测试数据...")
    test_messages = prepare_test_data()
    print(f"准备了 {len(test_messages)} 条测试消息")
    
    # 3. 清空现有数据并插入测试数据
    print("\n🧹 清空现有数据...")
    knowledge_base.delete_all_documents()
    
    print("📥 插入测试数据...")
    stats = knowledge_base.insert_documents_enhanced(test_messages)
    print(f"插入统计: {stats}")
    
    # 4. 等待数据被索引
    print("\n⏳ 等待数据被索引...")
    time.sleep(5)
    
    # 5. 测试不同的案例
    test_cases = [
        {
            "caseId": "test_company_info",
            "messages": [msg for msg in test_messages if msg["caseId"] == "case001"]
        },
        {
            "caseId": "test_selling_data", 
            "messages": [msg for msg in test_messages if msg["caseId"] == "case002"]
        },
        {
            "caseId": "test_negative_news",
            "messages": [msg for msg in test_messages if msg["caseId"] == "case003"]
        },
        {
            "caseId": "test_complaints",
            "messages": [msg for msg in test_messages if msg["caseId"] == "case004"]
        },
        {
            "caseId": "test_contact_request",
            "messages": [msg for msg in test_messages if msg["caseId"] == "case005"]
        },
        {
            "caseId": "test_spam",
            "messages": [msg for msg in test_messages if msg["caseId"] == "case006"]
        }
    ]
    
    # 6. 运行测试案例
    print(f"\n🧪 运行 {len(test_cases)} 个测试案例...")
    
    all_results = []
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"测试案例 {i}/{len(test_cases)}: {test_case['caseId']}")
        print(f"{'='*60}")
        
        # 运行增强版管道
        result = aggregator.run_enhanced_pipeline(
            messages=test_case["messages"],
            caseId=test_case["caseId"]
        )
        
        all_results.append(result)
        
        # 显示结果摘要
        print("\n📊 结果摘要:")
        review_res = result.get("review_res", {})
        for category, items in review_res.items():
            if isinstance(items, list) and items:
                print(f"  - {category}: {len(items)} 项风险")
            elif isinstance(items, dict) and items.get("hit_rule"):
                print(f"  - {category}: 检测到风险")
        
        analysis_stats = result.get("analysis_stats", {})
        print(f"  - 处理时间: {result.get('processing_time', 0):.2f} 秒")
        print(f"  - 平均置信度: {analysis_stats.get('average_confidence', 0):.3f}")
    
    # 7. 生成总结报告
    print(f"\n{'='*60}")
    print("📋 总结报告")
    print(f"{'='*60}")
    
    total_risks = 0
    total_time = 0
    
    for result in all_results:
        review_res = result.get("review_res", {})
        case_risks = 0
        
        for category, items in review_res.items():
            if isinstance(items, list):
                case_risks += len(items)
            elif isinstance(items, dict) and items.get("hit_rule"):
                case_risks += 1
        
        total_risks += case_risks
        total_time += result.get("processing_time", 0)
        
        print(f"案例 {result['id']}: {case_risks} 个风险, {result.get('processing_time', 0):.2f}s")
    
    print("\n📊 整体统计:")
    print(f"  - 总测试案例: {len(test_cases)}")
    print(f"  - 总检测风险: {total_risks}")
    print(f"  - 总处理时间: {total_time:.2f} 秒")
    print(f"  - 平均处理时间: {total_time / len(test_cases):.2f} 秒/案例")
    
    # 8. 保存详细结果
    output_file = "enhanced_pipeline_test_results.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 详细结果已保存到: {output_file}")
    print("\n✅ 测试完成!")
    
    return all_results


def test_single_case():
    """测试单个案例"""
    print("🧪 测试单个案例...")
    
    # 使用demo.py中的真实案例数据
    messages = [
        {"id": 975, "type": "USER", "msg": "Please help me to change may email address in kucoin Registered email address: <EMAIL> My new email address: <EMAIL> Account balance 3061,06 usdt Registration date July 10 2025"},
        {"id": 976, "type": "AGENT", "msg": 'Request ${********} "null" was closed and merged into this request.'},
        {"id": 977, "type": "AGENT", "msg": 'Request ${********} "null" was closed and merged into this request.'},
        {"id": 978, "type": "USER", "msg": "Hello"},
        {"id": 979, "type": "AGENT", "msg": "Hello! Thank you for contacting KuCoin customer support. I'm here to assist you with your email change request. I can see that you want to change your registered <NAME_EMAIL> to <EMAIL>."},
    ]
    
    # 初始化组件
    knowledge_base = EnhancedRetrievalCore()
    aggregator = EnhancedRetrievalAggregator(knowledge_base)
    
    # 插入一些相关数据到知识库
    test_data = prepare_test_data()
    knowledge_base.delete_all_documents()
    knowledge_base.insert_documents_enhanced(test_data)
    time.sleep(3)
    
    # 运行管道
    result = aggregator.run_enhanced_pipeline(messages, "real_case_test")
    
    print("\n结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    return result


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="测试增强版检索管道")
    parser.add_argument("--mode", choices=["full", "single"], default="full", 
                       help="测试模式: full=完整测试, single=单案例测试")
    
    args = parser.parse_args()
    
    if args.mode == "full":
        test_enhanced_pipeline()
    else:
        test_single_case()
