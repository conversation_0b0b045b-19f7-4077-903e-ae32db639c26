#!/usr/bin/env python3
"""
测试query配置文件加载
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dc_ai_red_line_review.main_pipe import BasicPipeline


def test_query_config_loading():
    """测试query配置文件加载"""
    print("=" * 50)
    print("测试query配置文件加载")
    print("=" * 50)

    try:
        pipeline = BasicPipeline()

        # 测试加载配置
        category_queries = pipeline._load_query_config()

        print(f"✅ 配置文件加载成功")
        print(f"类别数量: {len(category_queries)}")

        # 打印每个类别的详细信息
        for category, queries in category_queries.items():
            print(f"\n📂 {category}:")
            print(f"  Query数量: {len(queries)}")
            print(
                f"  英文Query: {[q for q in queries if all(ord(c) < 128 for c in q)]}"
            )
            print(
                f"  俄语Query: {[q for q in queries if any(ord(c) >= 1024 for c in q)]}"
            )

        return True
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_query_config_structure():
    """测试query配置文件结构"""
    print("\n" + "=" * 50)
    print("测试query配置文件结构")
    print("=" * 50)

    try:
        import json

        config_path = "dc_ai_red_line_review/query_config.json"
        with open(config_path, encoding="utf-8") as f:
            config = json.load(f)

        # 检查必要的结构
        assert "category_queries" in config, "Missing 'category_queries' key"

        category_queries = config["category_queries"]
        expected_categories = [
            "consulting_company_info",
            "selling_user_info",
            "negative_news",
            "major_complaints",
            "request_contact_information",
            "spam_messages",
            "sensitive_inquiry",
            "sensitive_reply",
        ]

        for category in expected_categories:
            assert category in category_queries, f"Missing category: {category}"
            assert isinstance(category_queries[category], list), (
                f"Category {category} should be a list"
            )
            assert len(category_queries[category]) > 0, (
                f"Category {category} should not be empty"
            )

        print("✅ 配置文件结构验证通过")
        print(f"包含所有必要的类别: {expected_categories}")

        # 统计语言分布
        total_english = 0
        total_russian = 0

        for category, queries in category_queries.items():
            english_count = len([q for q in queries if all(ord(c) < 128 for c in q)])
            russian_count = len([q for q in queries if any(ord(c) >= 1024 for c in q)])
            total_english += english_count
            total_russian += russian_count

        print(f"总计英文Query: {total_english}")
        print(f"总计俄语Query: {total_russian}")

        return True
    except Exception as e:
        print(f"❌ 配置文件结构验证失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("开始测试query配置...")

    # 运行测试
    test1 = test_query_config_structure()
    test2 = test_query_config_loading()

    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"配置文件结构测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"配置文件加载测试: {'✅ 通过' if test2 else '❌ 失败'}")

    if all([test1, test2]):
        print("\n🎉 所有测试通过！query配置正常")
    else:
        print("\n⚠️  部分测试失败，请检查配置文件")
