#!/usr/bin/env python3
"""
测试清理后的管道，验证RetrievalCore分块逻辑
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dc_ai_red_line_review.main_pipe import BasicPipeline


def test_pipeline_initialization():
    """测试管道初始化"""
    print("=" * 50)
    print("测试管道初始化")
    print("=" * 50)

    try:
        pipeline = BasicPipeline()
        print(f"✅ 管道初始化成功")
        print(f"RetrievalCore已初始化: {hasattr(pipeline, 'retrieval_core')}")
        print(f"旧的text_chunker已移除: {not hasattr(pipeline, 'text_chunker')}")
        return True
    except Exception as e:
        print(f"❌ 管道初始化失败: {e}")
        return False


def test_retrieval_core_chunking():
    """直接测试RetrievalCore分块功能"""
    print("\n" + "=" * 50)
    print("直接测试RetrievalCore分块功能")
    print("=" * 50)

    try:
        pipeline = BasicPipeline()

        messages = [
            {"id": 1, "type": "USER", "msg": "Test message 1"},
            {"id": 2, "type": "AGENT", "msg": "Test response 1"},
            {"id": 3, "type": "USER", "msg": "Test message 2"},
            {"id": 4, "type": "AGENT", "msg": "Test response 2"},
        ]

        chunk_docs, chunk_count = pipeline._split_messages_using_retrieval_core(
            messages, "test_direct"
        )
        print(f"✅ RetrievalCore分块成功")
        print(f"分块数量: {chunk_count}")
        print(f"分块类型: {type(chunk_docs[0]) if chunk_docs else 'None'}")
        return True
    except Exception as e:
        print(f"❌ RetrievalCore分块失败: {e}")
        return False


def test_small_chunk_processing():
    """测试小分块处理逻辑（简化版，无token计算）"""
    print("\n" + "=" * 50)
    print("测试小分块处理逻辑（简化版）")
    print("=" * 50)

    try:
        pipeline = BasicPipeline()

        # 使用较少的消息，应该产生少于10个分块
        messages = [
            {"id": 1, "type": "USER", "msg": "Hello"},
            {"id": 2, "type": "AGENT", "msg": "Hi there!"},
            {"id": 3, "type": "USER", "msg": "How are you?"},
            {"id": 4, "type": "AGENT", "msg": "I'm doing well, thank you!"},
        ]

        result = pipeline.run(messages=messages, caseId="test_small")
        print(f"✅ 小分块处理成功（直接LLM顺序处理）")
        print(f"结果键: {list(result.get('review_res', {}).keys())}")
        return True
    except Exception as e:
        print(f"❌ 小分块处理失败: {e}")
        return False


def test_large_chunk_processing():
    """测试大分块处理逻辑（多query检索+去重+分类审核）"""
    print("\n" + "=" * 50)
    print("测试大分块处理逻辑（多query检索+去重+分类审核）")
    print("=" * 50)

    try:
        pipeline = BasicPipeline()

        # 创建包含各种类型内容的大量消息
        messages = []

        # 添加联系方式相关消息
        for i in range(10):
            messages.extend(
                [
                    {
                        "id": i * 10 + 1,
                        "type": "USER",
                        "msg": f"我的联系方式是电话：138{i:04d}5678，邮箱：user{i}@example.com，微信：wx{i}123",
                    },
                    {
                        "id": i * 10 + 2,
                        "type": "AGENT",
                        "msg": f"好的，我已记录您的联系信息。如有问题会通过电话138{i:04d}5678联系您。",
                    },
                ]
            )

        # 添加政府询问相关消息
        for i in range(10):
            messages.extend(
                [
                    {
                        "id": i * 10 + 3,
                        "type": "USER",
                        "msg": f"税务局要求我提供交易记录，请问公司注册地址在哪里？监管机构需要合规报告。",
                    },
                    {
                        "id": i * 10 + 4,
                        "type": "AGENT",
                        "msg": f"关于政府监管和合规要求，我们会配合提供相关文件。公司注册地址是XXX。",
                    },
                ]
            )

        # 添加敏感内容相关消息
        for i in range(10):
            messages.extend(
                [
                    {
                        "id": i * 10 + 5,
                        "type": "USER",
                        "msg": f"这个平台是否涉及洗钱？我担心有违法风险，是否有欺诈行为？",
                    },
                    {
                        "id": i * 10 + 6,
                        "type": "AGENT",
                        "msg": f"我们严格遵守反洗钱法规，所有交易都是合法合规的，不存在欺诈风险。",
                    },
                ]
            )

        # 添加技术系统相关消息
        for i in range(10):
            messages.extend(
                [
                    {
                        "id": i * 10 + 7,
                        "type": "USER",
                        "msg": f"系统故障了，无法登录后台，数据库连接失败，服务器响应超时。",
                    },
                    {
                        "id": i * 10 + 8,
                        "type": "AGENT",
                        "msg": f"技术支持团队正在处理内部系统问题，服务器维护中，请稍后重试。",
                    },
                ]
            )

        print(f"创建了 {len(messages)} 条包含多种类型内容的测试消息")

        result = pipeline.run(messages=messages, caseId="test_large_multiquery")
        print(f"✅ 大分块处理成功（多query检索+去重+分类审核）")
        print(f"结果键: {list(result.get('review_res', {}).keys())}")

        # 打印各类别的结果统计
        review_res = result.get("review_res", {})
        for category, content in review_res.items():
            if isinstance(content, list):
                print(f"  - {category}: {len(content)} 项结果")
            elif isinstance(content, dict):
                values_count = len(content.get("values", []))
                print(
                    f"  - {category}: {values_count} 个值，hit_rule: {content.get('hit_rule', False)}"
                )

        return True
    except Exception as e:
        print(f"❌ 大分块处理失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("开始测试简化后的管道...")

    # 运行测试
    test1 = test_pipeline_initialization()
    test2 = test_retrieval_core_chunking()
    test3 = test_small_chunk_processing()
    test4 = test_large_chunk_processing()

    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"管道初始化测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"RetrievalCore分块测试: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"小分块处理测试: {'✅ 通过' if test3 else '❌ 失败'}")
    print(f"大分块处理测试: {'✅ 通过' if test4 else '❌ 失败'}")

    if all([test1, test2, test3, test4]):
        print("\n🎉 所有测试通过！简化逻辑成功")
        print("\n📝 处理逻辑说明:")
        print("  - 分块数 < 10: 直接LLM顺序处理，无token计算")
        print("  - 分块数 >= 10: 向量存储 + 占位函数处理")
    else:
        print("\n⚠️  部分测试失败，请检查配置和依赖")
