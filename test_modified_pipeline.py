#!/usr/bin/env python3
"""
测试清理后的管道，验证RetrievalCore分块逻辑
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dc_ai_red_line_review.main_pipe import BasicPipeline


def test_pipeline_initialization():
    """测试管道初始化"""
    print("=" * 50)
    print("测试管道初始化")
    print("=" * 50)

    try:
        pipeline = BasicPipeline()
        print(f"✅ 管道初始化成功")
        print(f"RetrievalCore已初始化: {hasattr(pipeline, 'retrieval_core')}")
        print(f"旧的text_chunker已移除: {not hasattr(pipeline, 'text_chunker')}")
        return True
    except Exception as e:
        print(f"❌ 管道初始化失败: {e}")
        return False


def test_retrieval_core_chunking():
    """直接测试RetrievalCore分块功能"""
    print("\n" + "=" * 50)
    print("直接测试RetrievalCore分块功能")
    print("=" * 50)

    try:
        pipeline = BasicPipeline()

        messages = [
            {"id": 1, "type": "USER", "msg": "Test message 1"},
            {"id": 2, "type": "AGENT", "msg": "Test response 1"},
            {"id": 3, "type": "USER", "msg": "Test message 2"},
            {"id": 4, "type": "AGENT", "msg": "Test response 2"},
        ]

        chunk_docs, chunk_count = pipeline._split_messages_using_retrieval_core(
            messages, "test_direct"
        )
        print(f"✅ RetrievalCore分块成功")
        print(f"分块数量: {chunk_count}")
        print(f"分块类型: {type(chunk_docs[0]) if chunk_docs else 'None'}")
        return True
    except Exception as e:
        print(f"❌ RetrievalCore分块失败: {e}")
        return False


def test_small_chunk_processing():
    """测试小分块处理逻辑（简化版，无token计算）"""
    print("\n" + "=" * 50)
    print("测试小分块处理逻辑（简化版）")
    print("=" * 50)

    try:
        pipeline = BasicPipeline()

        # 使用较少的消息，应该产生少于10个分块
        messages = [
            {"id": 1, "type": "USER", "msg": "Hello"},
            {"id": 2, "type": "AGENT", "msg": "Hi there!"},
            {"id": 3, "type": "USER", "msg": "How are you?"},
            {"id": 4, "type": "AGENT", "msg": "I'm doing well, thank you!"},
        ]

        result = pipeline.run(messages=messages, caseId="test_small")
        print(f"✅ 小分块处理成功（直接LLM顺序处理）")
        print(f"结果键: {list(result.get('review_res', {}).keys())}")
        return True
    except Exception as e:
        print(f"❌ 小分块处理失败: {e}")
        return False


def test_large_chunk_processing():
    """测试大分块处理逻辑（向量存储）"""
    print("\n" + "=" * 50)
    print("测试大分块处理逻辑（向量存储）")
    print("=" * 50)

    try:
        pipeline = BasicPipeline()

        # 创建大量消息，应该产生10个或更多分块
        messages = []
        for i in range(50):  # 创建50条消息
            messages.append(
                {
                    "id": i * 2 + 1,
                    "type": "USER",
                    "msg": f"This is a long user message number {i + 1}. "
                    * 30,  # 重复30次使消息更长
                }
            )
            messages.append(
                {
                    "id": i * 2 + 2,
                    "type": "AGENT",
                    "msg": f"This is a long agent response number {i + 1}. "
                    * 30,  # 重复30次使消息更长
                }
            )

        result = pipeline.run(messages=messages, caseId="test_large")
        print(f"✅ 大分块处理成功（向量存储+占位函数）")
        print(f"结果键: {list(result.get('review_res', {}).keys())}")
        return True
    except Exception as e:
        print(f"❌ 大分块处理失败: {e}")
        return False


if __name__ == "__main__":
    print("开始测试简化后的管道...")

    # 运行测试
    test1 = test_pipeline_initialization()
    test2 = test_retrieval_core_chunking()
    test3 = test_small_chunk_processing()
    test4 = test_large_chunk_processing()

    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"管道初始化测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"RetrievalCore分块测试: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"小分块处理测试: {'✅ 通过' if test3 else '❌ 失败'}")
    print(f"大分块处理测试: {'✅ 通过' if test4 else '❌ 失败'}")

    if all([test1, test2, test3, test4]):
        print("\n🎉 所有测试通过！简化逻辑成功")
        print("\n📝 处理逻辑说明:")
        print("  - 分块数 < 10: 直接LLM顺序处理，无token计算")
        print("  - 分块数 >= 10: 向量存储 + 占位函数处理")
    else:
        print("\n⚠️  部分测试失败，请检查配置和依赖")
