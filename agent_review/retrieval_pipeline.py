import json
import time
from typing import Any

from agent_review.enhanced_retrieval_aggregator import EnhancedRetrievalAggregator
from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore


class RetrievalBasedPipeline:
    """基于检索的管道，提供与BasicPipeline兼容的接口."""
    
    def __init__(self):
        """初始化检索管道."""
        print("🔧 初始化检索管道...")
        
        # 初始化检索组件
        self.knowledge_base = EnhancedRetrievalCore()
        self.aggregator = EnhancedRetrievalAggregator(self.knowledge_base)
        
        print("✅ 检索管道初始化完成")
    
    def run(self, messages: list[dict], caseId: str = "") -> dict[str, Any]:
        """运行检索管道，与BasicPipeline.run()接口兼容.
        
        Args:
            messages: 消息列表，格式: [{"id": int, "type": str, "msg": str}, ...]
            caseId: 案例ID
            
        Returns:
            与BasicPipeline兼容的结果格式:
            {
                "id": caseId,
                "review_res": {
                    "sensitive_inquiry": [...],
                    "sensitive_reply": [...],
                    "key_contact": {...},
                    "government_inquiry": [...],
                    "internal_system": {...}
                }
            }
        """
        print(f"🚀 开始处理案例: {caseId} (检索模式)")
        print(f"📝 消息数量: {len(messages)}")
        
        start_time = time.time()
        
        try:
            # 使用增强版聚合器处理
            result = self.aggregator.run_enhanced_pipeline(
                messages=messages,
                caseId=caseId,
                max_workers=6
            )
            
            # 提取核心结果，保持与BasicPipeline兼容
            compatible_result = {
                "id": result["id"],
                "review_res": result["review_res"]
            }
            
            # 可选：添加额外的统计信息作为元数据
            if "analysis_stats" in result:
                compatible_result["_retrieval_stats"] = result["analysis_stats"]
            if "processing_time" in result:
                compatible_result["_processing_time"] = result["processing_time"]
            
            print(f"✅ 案例 {caseId} 处理完成 (检索模式)")
            print(f"⏱️  处理时间: {time.time() - start_time:.2f} 秒")
            
            return compatible_result
            
        except Exception as e:
            print(f"❌ 处理案例 {caseId} 时出错: {e}")
            
            # 返回空结果，保持格式兼容
            return {
                "id": caseId,
                "review_res": {
                    "sensitive_inquiry": [],
                    "sensitive_reply": [],
                    "key_contact": {"hit_rule": False, "values": [], "matched_ids": []},
                    "government_inquiry": [],
                    "internal_system": {"hit_rule": False, "values": [], "matched_ids": []}
                },
                "_error": str(e),
                "_processing_time": time.time() - start_time
            }
    
    def insert_training_data(self, messages: list[dict]) -> dict[str, Any]:
        """插入训练数据到知识库.
        
        Args:
            messages: 消息列表
            
        Returns:
            插入统计信息
        """
        print(f"📥 插入训练数据: {len(messages)} 条消息")
        
        try:
            stats = self.knowledge_base.insert_documents_enhanced(messages)
            print(f"✅ 数据插入完成: {stats}")
            return stats
        except Exception as e:
            print(f"❌ 数据插入失败: {e}")
            return {"error": str(e)}
    
    def clear_knowledge_base(self):
        """清空知识库."""
        print("🧹 清空知识库...")
        try:
            self.knowledge_base.delete_all_documents()
            print("✅ 知识库已清空")
        except Exception as e:
            print(f"❌ 清空知识库失败: {e}")
    
    def get_knowledge_base_stats(self) -> dict[str, Any]:
        """获取知识库统计信息."""
        try:
            doc_count = self.knowledge_base.doc_store.count_documents()
            return {
                "document_count": doc_count,
                "status": "healthy" if doc_count > 0 else "empty"
            }
        except Exception as e:
            return {
                "document_count": 0,
                "status": "error",
                "error": str(e)
            }


def create_retrieval_pipeline() -> RetrievalBasedPipeline:
    """创建检索管道实例的工厂函数."""
    return RetrievalBasedPipeline()


# 为了向后兼容，提供别名
RetrievalPipeline = RetrievalBasedPipeline


if __name__ == "__main__":
    # 演示用法
    print("🧪 演示检索管道用法...")
    
    # 创建管道
    pipeline = RetrievalBasedPipeline()
    
    # 检查知识库状态
    stats = pipeline.get_knowledge_base_stats()
    print(f"📊 知识库状态: {stats}")
    
    # 如果知识库为空，插入一些示例数据
    if stats["document_count"] == 0:
        print("📝 插入示例数据...")
        sample_data = [
            {"id": 1, "type": "USER", "msg": "What is the company registered address?", "caseId": "sample"},
            {"id": 2, "type": "AGENT", "msg": "Our company is registered in Seychelles", "caseId": "sample"},
            {"id": 3, "type": "USER", "msg": "I want to sell user data", "caseId": "sample"},
            {"id": 4, "type": "USER", "msg": "Holy shit this is terrible", "caseId": "sample"},
        ]
        pipeline.insert_training_data(sample_data)
        
        # 等待索引
        print("⏳ 等待数据被索引...")
        time.sleep(3)
    
    # 测试处理
    test_messages = [
        {"id": 1, "type": "USER", "msg": "Where is your company located?"},
        {"id": 2, "type": "AGENT", "msg": "Thank you for your inquiry about our location"},
    ]
    
    result = pipeline.run(test_messages, "demo_case")
    
    print("\n📋 处理结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    print("\n✅ 演示完成!")
