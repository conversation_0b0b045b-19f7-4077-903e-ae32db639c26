import os
from textwrap import dedent

import httpx
from dotenv import load_dotenv
from smolagents import CodeAgent, OpenAIServerModel

from dc_ai_red_line_review.retrieval_core import RetrievalCore
from agent_review.retrieval_tool import RetrieverTool
from agent_review.text_to_json_converter import convert_agent_text_to_json

load_dotenv(override=True)


class NoThinkOpenAIServerModel(OpenAIServerModel):
    """关闭think模式的OpenAI服务器模型."""

    def generate(
        self,
        messages,
        stop_sequences=None,
        response_format=None,
        tools_to_call_from=None,
        **kwargs,
    ):
        # 确保extra_body参数被正确设置
        kwargs["extra_body"] = {"chat_template_kwargs": {"enable_thinking": False}}
        return super().generate(
            messages, stop_sequences, response_format, tools_to_call_from, **kwargs
        )


class AgenticRAG:
    def __init__(self):
        self.knowledge_base = RetrievalCore()

        retriever_tool = RetrieverTool(self.knowledge_base)

        self.agent = CodeAgent(
            tools=[retriever_tool],
            model=NoThinkOpenAIServerModel(
                model_id=os.environ["MODEL_NAME"],
                api_base=os.environ["QWQ_BASE_URL"],
                api_key=os.environ["QWQ_API_KEY"],
                client_kwargs={"http_client": httpx.Client(verify=False, timeout=60.0)},
            ),
            max_steps=20,
            verbosity_level=2,
        )

        self.agent_prompt = """
You are a content reviewer analyzing customer service conversations.

TASK: Identify 6 types of sensitive content and extract exact quotes with their message IDs.

EXTRACTION RULE: Copy text exactly as written in the conversation and include the corresponding message ID.

CATEGORIES:
1. consulting_company_info: Questions about company location/address
2. selling_user_info: Offers to buy/sell user data
3. negative_news: Rumors about platform issues
4. major_complaints: Angry users threatening external action
5. request_contact_information: Staff asking for personal contacts
6. spam_messages: Inappropriate language from staff

INSTRUCTIONS:
- Use the retriever tool to search for relevant content in each category
- The retriever returns documents in this format:
  Document 0:
    Content: [message content]
    Message_ID: [actual message ID]
    Type: USER/AGENT
    Score: [relevance score]
- Write ONLY clean Python code - NO HTML/XML tags, NO markdown syntax, NO angle brackets (<>)
- Each code block must be complete and syntactically correct Python code
- Never use any formatting characters like <, >, [, ] in code generation
- Code must be executable Python without any special formatting
- Use the actual Message_ID from the retrieval results, not document indices
- Only mark as FOUND if the content truly matches the category definition
- Standard customer service language is NOT sensitive content

IMPORTANT: Be very strict about what qualifies as sensitive content:
- consulting_company_info: Only actual questions about company location/address
- selling_user_info: Only explicit offers to buy/sell user data
- negative_news: Only rumors or negative claims about platform
- major_complaints: Only angry threats of external action
- request_contact_information: Only staff asking for personal contact info
- spam_messages: Only inappropriate/unprofessional language

OUTPUT FORMAT: Return your analysis as a valid JSON object following this exact structure:

{
  "consulting_company_info": {
    "hit_rule": true,
    "values": [
      {"content": "Hi 我要報稅 機關那邊要我提供交易所主體所在地", "message_id": 7},
      {"content": "您好，請問您是想咨詢KuCoin的注冊地址嗎？", "message_id": 10}
    ],
    "message_ids": [7, 10]
  },
  "selling_user_info": {
    "hit_rule": false,
    "values": [],
    "message_ids": []
  },
  "negative_news": {
    "hit_rule": false,
    "values": [],
    "message_ids": []
  },
  "major_complaints": {
    "hit_rule": false,
    "values": [],
    "message_ids": []
  },
  "request_contact_information": {
    "hit_rule": false,
    "values": [],
    "message_ids": []
  },
  "spam_messages": {
    "hit_rule": false,
    "values": [],
    "message_ids": []
  }
}

CRITICAL:
- Return ONLY valid JSON, no other text or formatting
- Ensure all message_id values are integers, not strings
- For each category, set hit_rule to true only if you found matching content
- Include exact quotes in the values array with their corresponding message_ids
- Be thorough in your search and accurate in extracting message IDs
- NEVER use any angle brackets, HTML tags, or markdown in your code
- All code must be pure Python without any formatting characters

"""

    def insert_data(self, caseId: str, messages: list[dict]):
        """Insert messages into knowledge base with case ID.

        Args:
            caseId: The case identifier
            messages: List of message dictionaries containing 'msg' field
        """
        # Add caseId to each message in a list comprehension
        documents = [{**msg, "caseId": caseId} for msg in messages]

        # Insert documents directly
        self.knowledge_base.insert_documents(documents=documents, content_key="msg")

    def run(self) -> dict:
        """Run the agent to get a JSON formatted answer with fallback mechanism."""
        import json
        import re

        agent_result = self.agent.run(dedent(self.agent_prompt))

        try:
            if isinstance(agent_result, str):
                json_match = re.search(r"\{.*\}", agent_result, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    json_result = json.loads(json_str)
                    return json_result
                else:
                    json_result = json.loads(agent_result)
                    return json_result
            elif isinstance(agent_result, dict):
                return agent_result
            else:
                raise ValueError(f"Unexpected agent result type: {type(agent_result)}")

        except (json.JSONDecodeError, ValueError) as e:
            print(f"Direct JSON parsing failed: {e}")
            print("Falling back to text-to-JSON conversion...")

            try:
                json_result = convert_agent_text_to_json(agent_result)
                print("Fallback conversion successful")
                return json_result
            except Exception as fallback_error:
                print(f"Fallback conversion also failed: {fallback_error}")
                print(f"Original agent result: {agent_result}")

                return self._get_empty_response()

    def _get_empty_response(self) -> dict:
        """当解析失败时返回空响应结构."""
        categories = [
            "consulting_company_info",
            "selling_user_info",
            "negative_news",
            "major_complaints",
            "request_contact_information",
            "spam_messages",
        ]

        empty_response = {}
        for category in categories:
            empty_response[category] = {
                "hit_rule": False,
                "values": [],
                "message_ids": [],
            }

        return empty_response


if __name__ == "__main__":
    review_agent = AgenticRAG()

    caseId = "10066130"
    messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "customer",
        },
        {
            "id": 2,
            "type": "USER",
            "msg": "customer support",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "support",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "KuCoin Pay Menu",
        },
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support. I understand your frustration and rest assured that I will help you through out this process. I would appreciate if you could give me 2-3 minutes to understand your issue and address the best possible solution as soon as possible. Thank you for your patience.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line while I transfer you to our relevant representatives for further assistance.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。",
        },
        {
            "id": 9,
            "type": "USER",
            "msg": "好的",
        },
        {
            "id": 10,
            "type": "AGENT",
            "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？",
        },
        {
            "id": 11,
            "type": "USER",
            "msg": "是的",
        },
        {
            "id": 12,
            "type": "USER",
            "msg": "報稅機關要求我提供",
        },
        {
            "id": 13,
            "type": "AGENT",
            "msg": "好的請稍等",
        },
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mah&eacute; , Republic of Seychelles",
        },
        {
            "id": 15,
            "type": "USER",
            "msg": "謝謝你",
        },
        {
            "id": 16,
            "type": "AGENT",
            "msg": "不客气",
        },
        {
            "id": 17,
            "type": "AGENT",
            "msg": "請問還有其他可以幫助您的嗎？",
        },
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話，如果您有任何其他問題，請隨時與我們聯繫。我們提供7*24小時客戶支援！😊",
        },
    ]

    # 先插入数据到知识库
    review_agent.insert_data(caseId, messages)

    # 然后运行分析
    answer = review_agent.run()
    print(answer)
