import json
import os
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dataclasses import dataclass

from agent_review.enhanced_retrieval_core import EnhancedRetrieval<PERSON>ore


@dataclass
class RetrievalResult:
    """Retrieval result data class."""

    content: str
    message_id: str
    message_type: str
    score: float
    category: str
    start_message_id: int = None
    end_message_id: int = None


class SimpleRetrievalAggregator:
    """Simplified retrieval aggregator supporting parallel retrieval and result aggregation with deduplication."""

    def __init__(self, knowledge_base: EnhancedRetrievalCore, config_path: str = None):
        """Initialize retrieval aggregator.

        Args:
            knowledge_base: Enhanced retrieval core instance
            config_path: Configuration file path, defaults to relative path
        """
        self.knowledge_base = knowledge_base

        # Load configuration file
        if config_path is None:
            config_path = os.path.join(
                os.path.dirname(__file__), "retrieval_queries.json"
            )

        with open(config_path, encoding="utf-8") as f:
            self.config = json.load(f)

        self.queries = self.config["retrieval_queries"]
        self.descriptions = self.config["category_descriptions"]
        self.search_config = self.config["search_config"]

    def _retrieve_for_category(
        self, category: str, queries: list[str]
    ) -> list[RetrievalResult]:
        """Execute retrieval for a single category.

        Args:
            category: Category name
            queries: Query list for this category

        Returns:
            List of retrieval results for this category
        """
        category_results = []

        for query in queries:
            try:
                # Execute retrieval
                documents = self.knowledge_base.search(
                    query=query, top_k=self.search_config["top_k_per_category"]
                )

                # Convert to standard format
                for doc in documents:
                    if doc.score >= self.search_config["score_threshold"]:
                        result = RetrievalResult(
                            content=doc.content,
                            message_id=str(doc.meta.get("message_id", "unknown")),
                            message_type=doc.meta.get("type", "unknown"),
                            score=doc.score,
                            category=category,
                            start_message_id=doc.meta.get("start_message_id"),
                            end_message_id=doc.meta.get("end_message_id"),
                        )
                        category_results.append(result)

            except Exception as e:
                print(
                    f"Retrieval failed - Category: {category}, Query: {query}, Error: {e}"
                )
                continue

        return category_results

    def retrieve_all_categories(
        self, max_workers: int = 6
    ) -> dict[str, list[RetrievalResult]]:
        """Retrieve all categories in parallel.

        Args:
            max_workers: Maximum number of parallel worker threads

        Returns:
            Dictionary of retrieval results grouped by category
        """
        results_by_category = {}

        # Use thread pool for parallel retrieval execution
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all retrieval tasks
            future_to_category = {
                executor.submit(
                    self._retrieve_for_category, category, queries
                ): category
                for category, queries in self.queries.items()
            }

            # Collect results
            for future in as_completed(future_to_category):
                category = future_to_category[future]
                try:
                    category_results = future.result()
                    results_by_category[category] = category_results
                    print(
                        f"✅ Category {category} retrieval completed, found {len(category_results)} results"
                    )
                except Exception as e:
                    print(f"❌ Category {category} retrieval failed: {e}")
                    results_by_category[category] = []

        return results_by_category

    def aggregate_and_deduplicate(
        self, results_by_category: dict[str, list[RetrievalResult]]
    ) -> tuple[list[RetrievalResult], dict[str, set[str]]]:
        """Aggregate and deduplicate retrieval results.

        Args:
            results_by_category: Retrieval results grouped by category

        Returns:
            (Deduplicated result list, message_id set for each category)
        """
        # Set for deduplication based on content and message_id
        seen_content_ids = set()
        aggregated_results = []
        category_message_ids = defaultdict(set)

        # Collect all results and sort by message_id
        all_results = []
        for category, results in results_by_category.items():
            for result in results:
                all_results.append(result)
                category_message_ids[category].add(result.message_id)

        # Sort by message_id (try to convert to int, fallback to string sorting)
        def sort_key(result):
            try:
                # Handle possible list format message_id
                if result.start_message_id is not None:
                    return result.start_message_id
                elif result.message_id.startswith("[") and result.message_id.endswith(
                    "]"
                ):
                    # Parse list format message_id, take the first one
                    import ast

                    id_list = ast.literal_eval(result.message_id)
                    return int(id_list[0]) if id_list else 0
                else:
                    return int(result.message_id)
            except (ValueError, SyntaxError):
                return float("inf")  # Put unparseable ones at the end

        all_results.sort(key=sort_key)

        # Deduplicate: based on content and message_id combination
        for result in all_results:
            content_id_key = f"{result.content[:100]}_{result.message_id}"  # Use first 100 chars of content + message_id as dedup key

            if content_id_key not in seen_content_ids:
                seen_content_ids.add(content_id_key)
                aggregated_results.append(result)

        # Limit total result count
        max_results = self.search_config["max_total_results"]
        if len(aggregated_results) > max_results:
            # Keep high-score results, sort by score and take top N
            aggregated_results.sort(key=lambda x: x.score, reverse=True)
            aggregated_results = aggregated_results[:max_results]
            # Re-sort by message_id
            aggregated_results.sort(key=sort_key)

        print("📊 Aggregation Statistics:")
        print(f"  - Total original results: {len(all_results)}")
        print(f"  - Results after deduplication: {len(aggregated_results)}")
        for category, msg_ids in category_message_ids.items():
            print(f"  - {category}: {len(msg_ids)} unique message_ids")

        return aggregated_results, dict(category_message_ids)

    def format_results_for_llm(self, aggregated_results: list[RetrievalResult]) -> str:
        """Format aggregated results for LLM input.

        Args:
            aggregated_results: Aggregated retrieval results

        Returns:
            Formatted string for LLM input
        """
        if not aggregated_results:
            return "No relevant retrieval results found."

        formatted_parts = ["Retrieved relevant conversation content:\n"]

        for i, result in enumerate(aggregated_results):
            formatted_parts.append(f"Document {i + 1}:")
            formatted_parts.append(f"  Content: {result.content}")
            formatted_parts.append(f"  Message ID: {result.message_id}")
            formatted_parts.append(f"  Type: {result.message_type}")
            formatted_parts.append(f"  Related Category: {result.category}")
            formatted_parts.append(f"  Relevance Score: {result.score:.4f}")
            formatted_parts.append("")  # Empty line separator

        return "\n".join(formatted_parts)

    def retrieve_and_aggregate(
        self, max_workers: int = 6
    ) -> tuple[str, dict[str, set[str]], list[RetrievalResult]]:
        """Execute complete retrieval and aggregation process.

        Args:
            max_workers: Maximum number of parallel worker threads

        Returns:
            (Formatted LLM input string, message_id set for each category, original result list)
        """
        print("🔍 Starting parallel retrieval for all categories...")

        # 1. Parallel retrieval for all categories
        results_by_category = self.retrieve_all_categories(max_workers)

        # 2. Aggregate and deduplicate
        print("\n📋 Starting aggregation and deduplication...")
        aggregated_results, category_message_ids = self.aggregate_and_deduplicate(
            results_by_category
        )

        # 3. Format for LLM input
        print("\n📝 Formatting results for LLM input...")
        formatted_input = self.format_results_for_llm(aggregated_results)

        print(
            f"\n✅ Retrieval aggregation completed, {len(aggregated_results)} valid results"
        )

        return formatted_input, category_message_ids, aggregated_results


if __name__ == "__main__":
    # Test code
    from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore

    # Initialize knowledge base
    knowledge_base = EnhancedRetrievalCore()

    # Initialize aggregator
    aggregator = SimpleRetrievalAggregator(knowledge_base)

    # Execute retrieval and aggregation
    formatted_input, category_message_ids, results = aggregator.retrieve_and_aggregate()

    print("\n" + "=" * 50)
    print("Formatted LLM Input:")
    print("=" * 50)
    print(formatted_input)

    print("\n" + "=" * 50)
    print("Message IDs by Category:")
    print("=" * 50)
    for category, msg_ids in category_message_ids.items():
        print(f"{category}: {sorted(list(msg_ids))}")
