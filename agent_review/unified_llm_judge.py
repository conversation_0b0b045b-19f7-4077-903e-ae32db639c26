import json
import os
import time

import httpx
from dotenv import load_dotenv
from openai import OpenAI
from pydantic import BaseModel, Field

from dc_ai_red_line_review.utils import get_logger, get_token_count

load_dotenv(override=True)


class UnifiedLLMJudge:
    """Unified LLM judge for one-time judgment of all categories of sensitive content"""

    def __init__(self):
        """Initialize LLM judge"""
        self.logger = get_logger(module_name="unified_llm_judge")

        # Initialize OpenAI client
        self.model_client = OpenAI(
            base_url=os.environ["QWQ_BASE_URL"],
            api_key=os.environ["QWQ_API_KEY"],
            http_client=httpx.Client(verify=False, timeout=60.0),
        )

        # Define system prompt, referencing existing red line review prompt
        self.system_prompt = """
You are a sensitive content reviewer. Your role is to review conversations between customer service representatives and users to identify specific sensitive information based on the provided rules and report them in the required format.

## CRITICAL INSTRUCTION: EXTRACT ONLY FROM CONVERSATION
- The categories below are for YOUR UNDERSTANDING of what to look for
- The "values" array must contain ONLY text that ACTUALLY EXISTS in the conversation
- DO NOT copy the rule descriptions or examples into the "values" array
- EXTRACT VERBATIM quotes from the conversation being reviewed

## Categories of Sensitive Information  
- **consulting_company_info**: Look for questions or discussions about company information such as:
  * Company registered/office address inquiries
  * Questions about company location (China/Singapore/Hong Kong/Malaysia)
  * Server location inquiries
  * Employee location questions
  * Customer service representative location questions
  * Company licensing inquiries
  
- **selling_user_info**: Look for offers to buy/sell user data or account information, including:
  * Targeted recruitment with excessive incentives
  * Unusually high rewards (e.g., "$2000-$7000 per day")
  * Requests to switch to private communication channels like Telegram
  * Suspicious links with abnormal formatting
  * Requests to disclose other users' information
  
- **negative_news**: Look for users inquiring about rumors claiming issues with KuCoin, such as:
  * Rumors of KuCoin running away, going bankrupt, or being sanctioned
  * Rumors of KuCoin leader/employee being arrested
  * Claims that the platform is unsafe, has experienced data breaches
  * Claims of large asset transfers from the platform
  * Rumors that the domain has been blocked
  * Concerns about insufficient platform reserves, asking for Proof of Reserves
  
- **major_complaints**: Look for extremely negative, dissatisfied, complaining, or angry users who threaten or mention exposing the company through social media, lawyers, police, etc.

- **request_contact_information**: Look for customer service representatives actively asking users for personal contact details such as:
  * Personal email, phone number, personal Telegram, WeChat, WhatsApp, Twitter, Facebook
  
- **spam_messages**: Look for responses from customer service representatives containing derogatory or offensive language, including:
  * holy shit, Jesus Christ, Jesus fuck, Jesus H. Christ, Jesus Harold Christ, Jesus wept, Jesus, Mary and Joseph

## OUTPUT FORMAT
Return JSON with hit_rule (boolean) and values (array of EXACT quotes from conversation) for each category.
**CRITICAL**: values must contain ONLY text that exists VERBATIM in the conversation being reviewed, NOT rule descriptions.

🚨 CRITICAL EXTRACTION REQUIREMENTS 🚨
1. VALUES MUST BE VERBATIM: Copy text EXACTLY as it appears in the conversation
2. NO MODIFICATIONS: Do not change, summarize, paraphrase, or translate any text
3. PRESERVE ORIGINAL LANGUAGE: Keep the exact language of the source text (Chinese stays Chinese, English stays English)
4. NO RULE TEXT: Do not copy text from rule descriptions or examples - ONLY from the conversation
5. NO INFERENCE: Do not generate content that doesn't exist in the conversation
6. EMPTY IF NO MATCH: Return [] if no exact matches are found in the conversation
7. SOURCE VERIFICATION: Every value must be traceable to specific text in the conversation
8. CHARACTER-LEVEL ACCURACY: Preserve exact characters, punctuation, spacing, and formatting
9. IGNORE EXAMPLES: All example text in the rules above should be ignored - only analyze the actual conversation
10. CONVERSATION ONLY: Extract ONLY from the conversation content between <|im_start|> and <|im_end|> tags

REMEMBER: You are extracting evidence from the conversation, not interpreting rules or creating content. NEVER translate or modify the original language.
"""

    def _make_llm_call(
        self, retrieval_content: str, content_tokens: int = None
    ) -> dict:
        """Call LLM for unified judgment

        Args:
            retrieval_content: Retrieved relevant content
            content_tokens: Token count of content (optional)

        Returns:
            LLM judgment result dictionary
        """

        # Define response format
        class UnifiedReviewResponse(BaseModel):
            consulting_company_info: dict = Field(
                description="Company information inquiry check results, containing hit_rule(bool) and values(list[str])"
            )
            selling_user_info: dict = Field(
                description="User information selling check results, containing hit_rule(bool) and values(list[str])"
            )
            negative_news: dict = Field(
                description="Negative news check results, containing hit_rule(bool) and values(list[str])"
            )
            major_complaints: dict = Field(
                description="Major complaint check results, containing hit_rule(bool) and values(list[str])"
            )
            request_contact_information: dict = Field(
                description="Contact information request check results, containing hit_rule(bool) and values(list[str])"
            )
            spam_messages: dict = Field(
                description="Abusive message check results, containing hit_rule(bool) and values(list[str])"
            )

        json_schema = UnifiedReviewResponse.model_json_schema()

        # Calculate token count
        prompt_tokens = get_token_count(self.system_prompt)
        if content_tokens is None:
            content_tokens = get_token_count(retrieval_content)
        total_tokens = prompt_tokens + content_tokens

        total_tokens_str = f"~{total_tokens:,} (system: {prompt_tokens:,}, content: {content_tokens:,})"
        self.logger.info(f"Starting LLM call with {total_tokens_str} tokens...")

        call_start_time = time.time()

        try:
            chat_completion = self.model_client.chat.completions.create(
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": retrieval_content},
                ],
                model=os.environ["MODEL_NAME"],
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "result",
                        "schema": json_schema,
                    },
                },
                extra_body={
                    "chat_template_kwargs": {"enable_thinking": False},
                },
                temperature=0.7,
                top_p=0.8,
            )
        except Exception as e:
            error_message = str(e).lower()
            self.logger.error(f"LLM call failed: {error_message}")
            raise ValueError(f"LLM call failed: {e}")

        # Validate response
        if not chat_completion.choices:
            raise ValueError("LLM returned empty choices")

        choice = chat_completion.choices[0]
        if not choice.message:
            raise ValueError("LLM returned empty message")

        response_content = choice.message.content
        self.logger.info(f"LLM response: {response_content}")

        # Parse JSON response
        try:
            parsed_response = json.loads(response_content)
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.error(f"Raw response content: {response_content}")
            raise ValueError(f"Invalid JSON response from LLM: {e}")

        call_end_time = time.time()
        api_duration = call_end_time - call_start_time
        self.logger.info(
            f"LLM call completed successfully in {api_duration:.2f}s "
            f"(tokens: {total_tokens_str})"
        )

        return parsed_response

    def judge_all_categories(
        self, retrieval_content: str, category_message_ids: dict[str, set[str]] = None
    ) -> dict:
        """Perform unified judgment for all categories

        Args:
            retrieval_content: Retrieved relevant content
            category_message_ids: Message_id set for each category (optional)

        Returns:
            Judgment result dictionary, compatible with existing red line review system format
        """
        self.logger.info("Starting unified LLM judgment...")

        # Call LLM for judgment
        llm_result = self._make_llm_call(retrieval_content)

        # Convert to format compatible with existing system
        result = self._convert_to_compatible_format(llm_result, category_message_ids)

        self.logger.info("Unified LLM judgment completed")
        return result

    def _convert_to_compatible_format(
        self, llm_result: dict, category_message_ids: dict[str, set[str]] = None
    ) -> dict:
        """Convert LLM result to format compatible with existing red line review system

        Args:
            llm_result: LLM original judgment result
            category_message_ids: Message_id set for each category

        Returns:
            Compatible format result dictionary
        """
        # Mapping relationships
        inquiry_mapping = {
            "consulting_company_info": "咨询公司信息",
            "selling_user_info": "兜售用户信息",
            "negative_news": "负面新闻",
            "major_complaints": "重大客诉",
        }

        reply_mapping = {
            "request_contact_information": "索要联系方式",
            "spam_messages": "辱骂信息",
        }

        # Build sensitive_inquiry results
        sensitive_inquiry_results = []
        for key, type_name in inquiry_mapping.items():
            if key in llm_result:
                result = llm_result[key].copy()
                result["type"] = type_name
                # Add message_ids information (if available)
                if category_message_ids and key in category_message_ids:
                    result["message_ids"] = list(category_message_ids[key])
                sensitive_inquiry_results.append(result)

        # Build sensitive_reply results
        sensitive_reply_results = []
        for key, type_name in reply_mapping.items():
            if key in llm_result:
                result = llm_result[key].copy()
                result["type"] = type_name
                # Add message_ids information (if available)
                if category_message_ids and key in category_message_ids:
                    result["message_ids"] = list(category_message_ids[key])
                sensitive_reply_results.append(result)

        return {
            "sensitive_inquiry": sensitive_inquiry_results,
            "sensitive_reply": sensitive_reply_results,
        }


if __name__ == "__main__":
    # Test code
    judge = UnifiedLLMJudge()

    # Test content
    test_content = """
Retrieved relevant conversation content:

Document 1:
  Content: 您好，請問您是想咨詢KuCoin的注冊地址嗎？
  Message ID: 10
  Type: AGENT
  Related Category: consulting_company_info
  Relevance Score: 0.8500

Document 2:
  Content: KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mahé , Republic of Seychelles
  Message ID: 14
  Type: AGENT
  Related Category: consulting_company_info
  Relevance Score: 0.9200
"""

    result = judge.judge_all_categories(test_content)

    print("Judgment Result:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
