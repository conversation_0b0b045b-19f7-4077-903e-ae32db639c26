# 检索+LLM管道使用说明

## 概述

这个增强版检索管道解决了您提到的问题：**使用检索先过滤数据，然后用LLM做综合判断**。它提供了与 `main_pipe.py` 兼容的接口，但使用了更智能的检索+LLM方式。

## 核心特性

### 🔍 两阶段处理流程
1. **检索阶段**：使用配置的查询词从知识库中检索相关对话片段
2. **LLM分析阶段**：将检索结果传给LLM进行综合判断和内容提取

### 🎯 智能风险检测
- 基于检索证据的精准判断
- LLM提供置信度评分
- 自动提取具体的风险内容
- 匹配原始消息ID

### 🔄 格式兼容
- 与 `main_pipe.py` 的 `BasicPipeline` 接口完全兼容
- 输出格式与现有系统一致
- 可以直接替换现有管道

## 快速开始

### 基本用法

```python
from agent_review.retrieval_pipeline import RetrievalBasedPipeline

# 创建管道
pipeline = RetrievalBasedPipeline()

# 处理消息（与BasicPipeline接口相同）
messages = [
    {"id": 1, "type": "USER", "msg": "What is your company address?"},
    {"id": 2, "type": "AGENT", "msg": "Our company is located in..."},
]

result = pipeline.run(messages, caseId="case_001")
print(result)
```

### 数据准备

在使用前，需要先向知识库插入训练数据：

```python
# 插入历史对话数据
training_data = [
    {"id": 1, "type": "USER", "msg": "公司地址在哪里？", "caseId": "train_001"},
    {"id": 2, "type": "AGENT", "msg": "我们公司注册在塞舌尔", "caseId": "train_001"},
    # ... 更多训练数据
]

pipeline.insert_training_data(training_data)
```

## 详细功能

### 1. 风险类别检测

系统检测以下6个风险类别：

- **consulting_company_info**: 公司信息查询
- **selling_user_info**: 用户信息销售
- **negative_news**: 负面新闻传播
- **major_complaints**: 重大投诉
- **request_contact_information**: 联系信息请求
- **spam_messages**: 垃圾/不当消息

### 2. 输出格式

```json
{
  "id": "case_001",
  "review_res": {
    "sensitive_inquiry": [
      {
        "hit_rule": true,
        "values": ["具体的风险内容"],
        "matched_ids": [[1, 2]],
        "confidence_score": 0.95,
        "category": "consulting_company_info"
      }
    ],
    "sensitive_reply": [],
    "key_contact": {"hit_rule": false, "values": [], "matched_ids": []},
    "government_inquiry": [],
    "internal_system": {"hit_rule": false, "values": [], "matched_ids": []}
  }
}
```

### 3. 管理功能

```python
# 检查知识库状态
stats = pipeline.get_knowledge_base_stats()
print(f"文档数量: {stats['document_count']}")

# 清空知识库
pipeline.clear_knowledge_base()
```

## 配置说明

### 检索配置

配置文件：`agent_review/retrieval_queries.json`

```json
{
  "search_config": {
    "top_k_per_category": 5,     // 每个类别返回的最大结果数
    "score_threshold": 0.6,      // 相关性分数阈值
    "max_total_results": 20      // 总结果数限制
  }
}
```

### 环境变量

确保以下环境变量已配置：

```bash
# OpenSearch配置
OPENSEARCH_HOST=your-opensearch-host
OPENSEARCH_USERNAME=admin
OPENSEARCH_PASSWORD=your-password
OPENSEARCH_INDEX=your-index

# LLM配置
QWQ_BASE_URL=your-llm-endpoint
QWQ_API_KEY=your-api-key
MODEL_NAME=your-model-name

# 嵌入模型配置
EMBEDDING_BASE_URL=your-embedding-endpoint
EMBEDDING_API_KEY=your-embedding-key
EMBEDDING_MODEL=your-embedding-model
EMBEDDING_DIMS=1024
```

## 性能优化

### 1. 并行处理
```python
# 调整并行工作线程数
result = pipeline.aggregator.run_enhanced_pipeline(
    messages=messages,
    caseId=caseId,
    max_workers=8  # 默认6
)
```

### 2. 批量处理
```python
# 批量插入数据
pipeline.insert_training_data(large_dataset)
```

## 与BasicPipeline的对比

| 特性 | BasicPipeline | RetrievalBasedPipeline |
|------|---------------|------------------------|
| 处理方式 | 直接LLM分析 | 检索+LLM分析 |
| 数据依赖 | 无 | 需要训练数据 |
| 准确性 | 中等 | 更高（基于证据） |
| 处理速度 | 快 | 中等（需要检索） |
| 可解释性 | 低 | 高（提供检索证据） |
| 置信度 | 无 | 有 |

## 故障排除

### 1. 检索无结果
```python
# 检查知识库状态
stats = pipeline.get_knowledge_base_stats()
if stats["document_count"] == 0:
    print("知识库为空，需要插入数据")
```

### 2. LLM调用失败
- 检查API配置和网络连接
- 确认模型名称正确
- 检查API密钥有效性

### 3. 性能问题
- 减少 `max_workers` 数量
- 调整 `top_k_per_category` 参数
- 优化训练数据质量

## 示例脚本

运行完整测试：
```bash
python test_enhanced_retrieval_pipeline.py --mode full
```

运行单案例测试：
```bash
python test_enhanced_retrieval_pipeline.py --mode single
```

演示基本用法：
```bash
python agent_review/retrieval_pipeline.py
```

## 注意事项

1. **数据质量**：训练数据的质量直接影响检索效果
2. **索引时间**：插入数据后需要等待几秒钟让OpenSearch建立索引
3. **内存使用**：大量数据可能需要更多内存
4. **API限制**：注意LLM API的调用频率限制

## 下一步

1. 根据实际业务数据调整检索查询词
2. 优化LLM提示词模板
3. 添加更多风险类别
4. 实现增量数据更新
