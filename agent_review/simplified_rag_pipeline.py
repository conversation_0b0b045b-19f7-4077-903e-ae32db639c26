import time

from dotenv import load_dotenv

from agent_review.enhanced_retrieval_core import Enhanced<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from agent_review.simple_retrieval_aggregator import SimpleRetrievalAggregator
from agent_review.unified_llm_judge import UnifiedLLMJudge
from dc_ai_red_line_review.utils import get_logger

load_dotenv(override=True)


class SimplifiedRAGPipeline:
    """Simplified RAG pipeline replacing Agent architecture, using 'one retrieval + one judgment' approach"""

    def __init__(
        self,
        min_content_length: int = 50,
        max_content_length: int = 1000,
        merge_window: int = 3,
        overlap_ratio: float = 0.1,
        config_path: str = None,
    ):
        """Initialize simplified RAG pipeline

        Args:
            min_content_length: Minimum content length, messages below this length will be merged
            max_content_length: Maximum content length, messages above this length will be chunked
            merge_window: Merge window size for merging consecutive short messages
            overlap_ratio: Overlap ratio when chunking
            config_path: Retrieval query configuration file path
        """
        self.logger = get_logger(module_name="simplified_rag_pipeline")

        # Initialize knowledge base
        self.knowledge_base = EnhancedRetrievalCore(
            min_content_length=min_content_length,
            max_content_length=max_content_length,
            merge_window=merge_window,
            overlap_ratio=overlap_ratio,
        )

        # Initialize retrieval aggregator
        self.retrieval_aggregator = SimpleRetrievalAggregator(
            knowledge_base=self.knowledge_base, config_path=config_path
        )

        # Initialize LLM judge
        self.llm_judge = UnifiedLLMJudge()

        self.logger.info("Simplified RAG pipeline initialization completed")

    def insert_data(
        self, caseId: str, messages: list[dict], verbose: bool = True
    ) -> dict:
        """Insert messages into knowledge base

        Args:
            caseId: Case identifier
            messages: Message list containing 'msg' field
            verbose: Whether to print detailed information

        Returns:
            Insertion statistics dictionary
        """
        # Add caseId to each message
        documents = [{**msg, "caseId": caseId} for msg in messages]

        # Use enhanced insertion method
        stats = self.knowledge_base.insert_documents_enhanced(
            documents=documents, content_key="msg"
        )

        if verbose:
            self.logger.info(f"📊 Data insertion statistics (Case: {caseId}):")
            self.logger.info(f"  • Original message count: {stats['total_input']}")
            self.logger.info(f"  • Processed chunk count: {stats['total_chunks']}")
            self.logger.info(f"  • Merged chunks: {stats['merged_count']}")
            self.logger.info(f"  • Split chunks: {stats['split_count']}")
            self.logger.info(f"  • Individual chunks: {stats['single_count']}")

            # Calculate compression ratio
            compression_ratio = (
                stats["total_chunks"] / stats["total_input"]
                if stats["total_input"] > 0
                else 0
            )
            self.logger.info(
                f"  • Compression ratio: {compression_ratio:.2f} ({stats['total_chunks']}/{stats['total_input']})"
            )

            if stats["merged_count"] > 0:
                self.logger.info(
                    f"  ✅ Successfully merged {stats['merged_count']} short conversation chunks"
                )
            if stats["split_count"] > 0:
                self.logger.info(
                    f"  ✅ Successfully split {stats['split_count']} long text chunks"
                )

        return stats

    def run(self, caseId: str = "", max_workers: int = 6) -> dict:
        """Run simplified RAG pipeline for sensitive content analysis

        Args:
            caseId: Case identifier
            max_workers: Maximum number of parallel worker threads for retrieval

        Returns:
            Analysis result dictionary, compatible with existing system format
        """
        start_time = time.time()
        self.logger.info(f"🚀 Starting analysis for case {caseId}...")

        try:
            # Step 1: Parallel retrieval for all categories
            self.logger.info("📋 Step 1: Executing parallel retrieval...")
            retrieval_start = time.time()

            formatted_input, category_message_ids, aggregated_results = (
                self.retrieval_aggregator.retrieve_and_aggregate(
                    max_workers=max_workers
                )
            )

            retrieval_time = time.time() - retrieval_start
            self.logger.info(
                f"✅ Retrieval completed, time taken: {retrieval_time:.2f}s"
            )

            # Step 2: Unified LLM judgment
            self.logger.info("🤖 Step 2: Executing unified LLM judgment...")
            judge_start = time.time()

            if not aggregated_results:
                self.logger.info(
                    "⚠️ No relevant content retrieved, returning empty result"
                )
                result = self._get_empty_result()
            else:
                result = self.llm_judge.judge_all_categories(
                    retrieval_content=formatted_input,
                    category_message_ids=category_message_ids,
                )

            judge_time = time.time() - judge_start
            self.logger.info(
                f"✅ LLM judgment completed, time taken: {judge_time:.2f}s"
            )

            # Step 3: Format final result
            final_result = self._format_final_result(result, caseId)

            total_time = time.time() - start_time
            self.logger.info(
                f"🎉 Case {caseId} analysis completed, total time: {total_time:.2f}s"
            )
            self.logger.info(
                f"   - Retrieval time: {retrieval_time:.2f}s ({retrieval_time / total_time * 100:.1f}%)"
            )
            self.logger.info(
                f"   - Judgment time: {judge_time:.2f}s ({judge_time / total_time * 100:.1f}%)"
            )

            return final_result

        except Exception as e:
            self.logger.error(f"❌ Case {caseId} analysis failed: {e}")
            return {
                "id": caseId,
                "review_res": self._get_empty_result(),
                "error": str(e),
            }

    def _get_empty_result(self) -> dict:
        """Get empty analysis result"""
        return {
            "sensitive_inquiry": [],
            "sensitive_reply": [],
        }

    def _format_final_result(self, analysis_result: dict, caseId: str) -> dict:
        """Format final result to ensure compatibility with existing system

        Args:
            analysis_result: LLM analysis result
            caseId: Case identifier

        Returns:
            Formatted final result
        """
        # Ensure correct result format
        formatted_result = {
            "id": caseId,
            "review_res": {
                "sensitive_inquiry": analysis_result.get("sensitive_inquiry", []),
                "sensitive_reply": analysis_result.get("sensitive_reply", []),
            },
        }

        # Statistics
        inquiry_count = len(formatted_result["review_res"]["sensitive_inquiry"])
        reply_count = len(formatted_result["review_res"]["sensitive_reply"])

        # Count hit categories
        hit_categories = []
        for item in formatted_result["review_res"]["sensitive_inquiry"]:
            if item.get("hit_rule", False):
                hit_categories.append(item.get("type", "Unknown Category"))
        for item in formatted_result["review_res"]["sensitive_reply"]:
            if item.get("hit_rule", False):
                hit_categories.append(item.get("type", "Unknown Category"))

        self.logger.info("📊 Analysis Result Statistics:")
        self.logger.info(f"  - Sensitive inquiry categories: {inquiry_count}")
        self.logger.info(f"  - Sensitive reply categories: {reply_count}")
        if hit_categories:
            self.logger.info(f"  - Hit categories: {', '.join(hit_categories)}")
        else:
            self.logger.info("  - No sensitive content found")

        return formatted_result

    def clear_knowledge_base(self):
        """Clear knowledge base"""
        self.knowledge_base.delete_all_documents()
        self.logger.info("🗑️ Knowledge base cleared")

    def get_insertion_stats(self) -> dict:
        """Get insertion configuration information"""
        return {
            "min_content_length": self.knowledge_base.min_content_length,
            "max_content_length": self.knowledge_base.max_content_length,
            "merge_window": self.knowledge_base.merge_window,
            "overlap_ratio": self.knowledge_base.overlap_ratio,
        }


if __name__ == "__main__":
    # Usage example
    pipeline = SimplifiedRAGPipeline(
        min_content_length=4000,
        max_content_length=32000,
        merge_window=10,
        overlap_ratio=0.15,
    )

    print("🚀 Simplified RAG pipeline initialization completed")
    print("📋 Current configuration:", pipeline.get_insertion_stats())

    # Test data
    caseId = "simplified_test_001"
    messages = [
        {"id": 1, "type": "USER", "msg": "customer"},
        {"id": 2, "type": "USER", "msg": "customer support"},
        {"id": 3, "type": "USER", "msg": "support"},
        {"id": 4, "type": "USER", "msg": "KuCoin Pay Menu"},
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。",
        },
        {"id": 9, "type": "USER", "msg": "好的"},
        {"id": 10, "type": "AGENT", "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？"},
        {"id": 11, "type": "USER", "msg": "是的"},
        {"id": 12, "type": "USER", "msg": "報稅機關要求我提供"},
        {"id": 13, "type": "AGENT", "msg": "好的請稍等"},
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mahé , Republic of Seychelles",
        },
        {"id": 15, "type": "USER", "msg": "謝謝你"},
        {"id": 16, "type": "AGENT", "msg": "不客气"},
        {"id": 17, "type": "AGENT", "msg": "請問還有其他可以幫助您的嗎？"},
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話。",
        },
    ]

    # Insert data
    stats = pipeline.insert_data(caseId, messages, verbose=True)

    # Run analysis
    result = pipeline.run(caseId)

    print("\n" + "=" * 50)
    print("Analysis Result:")
    print("=" * 50)
    import json

    print(json.dumps(result, ensure_ascii=False, indent=2))
