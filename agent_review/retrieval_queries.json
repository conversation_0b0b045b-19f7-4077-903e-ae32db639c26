{"retrieval_queries": {"consulting_company_info": ["company registered address location", "office address inquiry", "server location where", "employee location based", "customer service representative location", "company license permit", "зарегистрированный адрес компании", "адрес офиса запрос", "расположение сервера где", "местоположение сотрудников", "местоположение представителя службы поддержки", "лицензия компании разрешение"], "selling_user_info": ["recruitment customer support tasks", "high rewards daily payment", "bitcoin cryptocurrency payment", "switch private communication telegram", "account lookup data search", "provide modify account information", "sell user data information", "набор персонала поддержки задачи", "высокие награды ежедневная оплата", "биткоин криптовалюта платеж", "переключиться частная связь телеграм", "поиск данных аккаунта", "предоставить изменить информацию аккаунта", "продать пользовательские данные информация"], "negative_news": ["KuCoin running away bankruptcy rumors", "KuCoin sanctioned regulated", "leader employee arrested", "platform unsafe data breach", "wallet keys stolen", "large asset transfer", "domain blocked", "insufficient reserves proof", "KuCoin убегает слухи о банкротстве", "KuCoin санкционирован регулируется", "руководитель сотрудник арестован", "платформа небезопасна утечка данных", "ключи кошелька украдены", "крупный перевод активов", "домен заблокирован", "недостаточные резервы доказательство"], "major_complaints": ["extremely dissatisfied angry complaint", "threaten expose company", "social media exposure", "lawyer legal action", "report to police", "regulatory complaint", "крайне недоволен злая жалоба", "угрожать разоблачить компанию", "разоблачение в социальных сетях", "адвокат правовые действия", "сообщить в полицию", "жалоба регулятору"], "request_contact_information": ["customer service ask personal email", "customer service request phone number", "customer service ask telegram", "customer service request wechat", "customer service ask whatsapp", "customer service request twitter", "customer service ask facebook", "служба поддержки спрашивает личную почту", "служба поддержки запрашивает номер телефона", "служба поддержки спрашивает телеграм", "служба поддержки запрашивает вичат", "служба поддержки спрашивает ватсапп", "служба поддержки запрашивает твиттер", "служба поддержки спрашивает фейсбук"], "spam_messages": ["holy shit", "Jesus Christ", "Jesus fuck", "<PERSON>", "<PERSON>", "<PERSON> wept", "<PERSON>, <PERSON> and <PERSON>", "offensive language reply", "inappropriate response", "черт возьми", "<PERSON><PERSON><PERSON><PERSON>с Христос", "проклятие", "оскорбительный язык ответ", "неуместный ответ"]}, "category_descriptions": {"consulting_company_info": "Find messages where users inquire about company information such as registered address, office location, server location, employee location, licenses, etc.", "selling_user_info": "Find messages involving selling user information, recruiting internal personnel, high rewards, private communication channels, and other suspicious activities", "negative_news": "Find messages where users inquire about platform negative rumors, security issues, bankruptcy rumors, regulatory issues, etc.", "major_complaints": "Find messages where users show extreme dissatisfaction, threaten exposure, legal action, and other major complaints", "request_contact_information": "Find messages where customer service actively asks users for personal contact information such as email, phone, social media, etc.", "spam_messages": "Find messages containing inappropriate remarks and abusive language in customer service replies"}, "search_config": {"top_k_per_category": 5, "score_threshold": 0.6, "max_total_results": 20}}