import json
import os
from typing import Any

import httpx
from openai import OpenAI
from pydantic import BaseModel, Field


class MatchedContent(BaseModel):
    content: str = Field(description="Exact quote from conversation")
    message_id: int = Field(description="Message ID where the content was found")


class ReviewResult(BaseModel):
    hit_rule: bool = Field(description="Whether the rule was triggered")
    values: list[MatchedContent] = Field(
        description="List of matched content with message IDs from the conversation"
    )
    message_ids: list[int] = Field(
        description="List of message IDs containing the matched content"
    )


class UnifiedReviewResponse(BaseModel):
    consulting_company_info: ReviewResult = Field(
        description="Company information inquiry check results"
    )
    selling_user_info: ReviewResult = Field(
        description="User information selling check results"
    )
    negative_news: ReviewResult = Field(description="Negative news check results")
    major_complaints: ReviewResult = Field(description="Major complaint check results")
    request_contact_information: ReviewResult = Field(
        description="Contact information request check results"
    )
    spam_messages: ReviewResult = Field(description="Abusive message check results")


class TextToJsonConverter:
    """独立的文本到JSON转换器类."""

    def __init__(self):
        """初始化转换器，设置OpenAI客户端."""
        self.client = OpenAI(
            base_url=os.environ["QWQ_BASE_URL"],
            api_key=os.environ["QWQ_API_KEY"],
            http_client=httpx.Client(verify=False, timeout=60.0),
        )
        self.json_schema = UnifiedReviewResponse.model_json_schema()

    def convert_text_to_json(self, text_result: str) -> dict[str, Any]:
        """使用LLM将agent的文本输出转换为JSON格式.

        Args:
            text_result: agent输出的文本格式报告

        Returns:
            符合UnifiedReviewResponse结构的字典
        """
        conversion_prompt = f"""
Please convert the following agent text report into a valid JSON format that matches this schema:

AGENT TEXT REPORT:
{text_result}

REQUIRED JSON SCHEMA:
{json.dumps(self.json_schema, indent=2)}

CONVERSION RULES:
1. Extract information from each CATEGORY section
2. For each category, set "hit_rule" to true if "FOUND: Yes", false if "FOUND: No"
3. Extract quotes and their message IDs from the QUOTES section
4. If QUOTES shows "None", set "values" and "message_ids" to empty arrays
5. For each quote in format "- Message ID X: content", create a values entry with content and message_id
6. Ensure all message_id values are integers, not strings
7. Return ONLY valid JSON, no other text

The six categories are:
- consulting_company_info
- selling_user_info  
- negative_news
- major_complaints
- request_contact_information
- spam_messages
"""

        try:
            # 调用LLM进行转换
            chat_completion = self.client.chat.completions.create(
                messages=[
                    {
                        "role": "system",
                        "content": "You are a data conversion assistant. Convert text reports to JSON format exactly as specified.",
                    },
                    {"role": "user", "content": conversion_prompt},
                ],
                model=os.environ["MODEL_NAME"],
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "result",
                        "schema": self.json_schema,
                    },
                },
                extra_body={
                    "chat_template_kwargs": {"enable_thinking": False},
                },
                temperature=0.0,  # 确保一致的转换结果
            )

            # 获取响应内容
            response_content = chat_completion.choices[0].message.content

            # 解析JSON
            if response_content:
                json_result = json.loads(response_content)
                return json_result
            else:
                print("Empty response from conversion LLM")
                return self._get_empty_response()

        except Exception as e:
            print(f"Error in LLM text to JSON conversion: {e}")
            print(f"Original text: {text_result}")
            return self._get_empty_response()

    def _get_empty_response(self) -> dict[str, Any]:
        """当转换失败时返回空响应结构."""
        categories = [
            "consulting_company_info",
            "selling_user_info",
            "negative_news",
            "major_complaints",
            "request_contact_information",
            "spam_messages",
        ]

        empty_response = {}
        for category in categories:
            empty_response[category] = {
                "hit_rule": False,
                "values": [],
                "message_ids": [],
            }

        return empty_response


# 便利函数，用于直接调用转换
def convert_agent_text_to_json(text_result: str) -> dict[str, Any]:
    """便利函数：将agent文本输出转换为JSON.

    Args:
        text_result: agent输出的文本格式报告

    Returns:
        符合UnifiedReviewResponse结构的字典
    """
    converter = TextToJsonConverter()
    return converter.convert_text_to_json(text_result)
