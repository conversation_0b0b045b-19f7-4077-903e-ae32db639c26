import json
import os
import time
from dataclasses import dataclass
from typing import Any

import httpx
from dotenv import load_dotenv
from openai import OpenAI

from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore
from agent_review.simple_retrieval_aggregator import (
    RetrievalResult,
    SimpleRetrievalAggregator,
)

load_dotenv(override=True)


@dataclass
class EnhancedReviewResult:
    """增强版审查结果数据类"""

    category: str
    hit_rule: bool
    values: list[str]
    matched_ids: list[list[int]]
    confidence_score: float
    retrieval_evidence: list[RetrievalResult]


class EnhancedRetrievalAggregator(SimpleRetrievalAggregator):
    """增强版检索聚合器，集成LLM综合判断功能"""

    def __init__(self, knowledge_base: EnhancedRetrievalCore, config_path: str = None):
        """初始化增强版检索聚合器

        Args:
            knowledge_base: 增强检索核心实例
            config_path: 配置文件路径
        """
        super().__init__(knowledge_base, config_path)

        # 初始化LLM客户端
        self.model_client = OpenAI(
            base_url=os.environ["QWQ_BASE_URL"],
            api_key=os.environ["QWQ_API_KEY"],
            http_client=httpx.Client(verify=False, timeout=60.0),
        )

        # 加载LLM判断的提示词模板
        self.llm_prompts = self._load_llm_prompts()

    def _load_llm_prompts(self) -> dict[str, str]:
        """加载LLM判断的提示词模板"""
        return {
            "system_prompt": """你是一个专业的客服对话审查专家。你的任务是基于检索到的相关对话片段，判断是否存在特定类型的风险行为。

请仔细分析提供的检索证据，并给出准确的判断。对于每个类别，你需要：
1. 判断是否确实存在该类型的风险行为 (hit_rule: true/false)
2. 如果存在，提取具体的风险内容 (values)
3. 给出置信度评分 (0.0-1.0)

注意：
- 只有在有明确证据时才判断为风险行为
- 提取的内容必须是原文中的确切表述
- 置信度应该反映判断的确定程度""",
            "analysis_prompt": """基于以下检索到的对话片段，分析是否存在 "{category}" 类型的风险行为：

类别描述：{category_description}

检索证据：
{retrieval_evidence}

请分析并返回JSON格式的结果：
{{
    "hit_rule": boolean,
    "values": ["具体的风险内容1", "具体的风险内容2"],
    "confidence_score": float,
    "reasoning": "分析推理过程"
}}""",
        }

    def _call_llm_for_analysis(
        self, category: str, retrieval_results: list[RetrievalResult]
    ) -> dict[str, Any]:
        """调用LLM对检索结果进行分析判断

        Args:
            category: 风险类别
            retrieval_results: 检索结果列表

        Returns:
            LLM分析结果
        """
        if not retrieval_results:
            return {
                "hit_rule": False,
                "values": [],
                "confidence_score": 0.0,
                "reasoning": "无相关检索证据",
            }

        # 格式化检索证据
        evidence_parts = []
        for i, result in enumerate(retrieval_results):
            evidence_parts.append(f"证据 {i + 1} (相关性: {result.score:.4f}):")
            evidence_parts.append(f"  内容: {result.content}")
            evidence_parts.append(f"  消息ID: {result.message_id}")
            evidence_parts.append(f"  类型: {result.message_type}")
            evidence_parts.append("")

        evidence_text = "\n".join(evidence_parts)

        # 获取类别描述
        category_description = self.descriptions.get(category, "未知类别")

        # 构建分析提示词
        analysis_prompt = self.llm_prompts["analysis_prompt"].format(
            category=category,
            category_description=category_description,
            retrieval_evidence=evidence_text,
        )

        try:
            # 调用LLM
            response = self.model_client.chat.completions.create(
                model=os.environ["MODEL_NAME"],
                messages=[
                    {"role": "system", "content": self.llm_prompts["system_prompt"]},
                    {"role": "user", "content": analysis_prompt},
                ],
                temperature=0.1,
                response_format={"type": "json_object"},
            )

            # 解析响应
            result = json.loads(response.choices[0].message.content)

            # 验证和清理结果
            result.setdefault("hit_rule", False)
            result.setdefault("values", [])
            result.setdefault("confidence_score", 0.0)
            result.setdefault("reasoning", "")

            # 确保confidence_score在合理范围内
            result["confidence_score"] = max(
                0.0, min(1.0, float(result["confidence_score"]))
            )

            return result

        except Exception as e:
            print(f"❌ LLM分析失败 - 类别: {category}, 错误: {e}")
            return {
                "hit_rule": False,
                "values": [],
                "confidence_score": 0.0,
                "reasoning": f"LLM分析失败: {str(e)}",
            }

    def _find_matched_ids(
        self, values: list[str], original_messages: list[dict]
    ) -> list[list[int]]:
        """查找值在原始消息中的匹配ID

        Args:
            values: 提取的风险内容列表
            original_messages: 原始消息列表

        Returns:
            每个值对应的消息ID列表
        """
        matched_ids = []

        for value in values:
            ids = []
            for msg in original_messages:
                if value in msg.get("msg", ""):
                    ids.append(msg["id"])

            # 去重并保持顺序
            unique_ids = list(dict.fromkeys(ids))
            matched_ids.append(unique_ids)

        return matched_ids

    def enhanced_retrieve_and_analyze(
        self, original_messages: list[dict] = None, max_workers: int = 6
    ) -> tuple[dict[str, EnhancedReviewResult], dict[str, Any]]:
        """执行增强版检索和LLM分析

        Args:
            original_messages: 原始消息列表，用于匹配ID
            max_workers: 最大并行工作线程数

        Returns:
            (增强版分析结果, 统计信息)
        """
        print("🔍 开始增强版检索和分析流程...")

        # 1. 执行检索聚合
        print("\n📋 第一阶段：检索聚合...")
        results_by_category = self.retrieve_all_categories(max_workers)
        aggregated_results, category_message_ids = self.aggregate_and_deduplicate(
            results_by_category
        )

        # 2. LLM分析阶段
        print("\n🤖 第二阶段：LLM综合分析...")
        enhanced_results = {}
        analysis_stats = {
            "total_categories": len(self.queries),
            "categories_with_evidence": 0,
            "categories_with_risks": 0,
            "total_confidence": 0.0,
        }

        for category in self.queries.keys():
            print(f"\n🔍 分析类别: {category}")

            # 获取该类别的检索结果
            category_retrieval_results = [
                result for result in aggregated_results if result.category == category
            ]

            if category_retrieval_results:
                analysis_stats["categories_with_evidence"] += 1
                print(f"  📄 检索证据: {len(category_retrieval_results)} 条")
            else:
                print("  ⚠️  无检索证据")

            # LLM分析
            llm_result = self._call_llm_for_analysis(
                category, category_retrieval_results
            )

            # 查找匹配的消息ID
            matched_ids = []
            if original_messages and llm_result.get("values"):
                matched_ids = self._find_matched_ids(
                    llm_result["values"], original_messages
                )

            # 创建增强结果
            enhanced_result = EnhancedReviewResult(
                category=category,
                hit_rule=llm_result.get("hit_rule", False),
                values=llm_result.get("values", []),
                matched_ids=matched_ids,
                confidence_score=llm_result.get("confidence_score", 0.0),
                retrieval_evidence=category_retrieval_results,
            )

            enhanced_results[category] = enhanced_result

            # 更新统计
            if enhanced_result.hit_rule:
                analysis_stats["categories_with_risks"] += 1
            analysis_stats["total_confidence"] += enhanced_result.confidence_score

            print(f"  🎯 风险判断: {enhanced_result.hit_rule}")
            print(f"  📊 置信度: {enhanced_result.confidence_score:.3f}")
            if enhanced_result.values:
                print(f"  📝 提取内容: {len(enhanced_result.values)} 项")

        # 计算平均置信度
        if analysis_stats["total_categories"] > 0:
            analysis_stats["average_confidence"] = (
                analysis_stats["total_confidence"] / analysis_stats["total_categories"]
            )

        print("\n✅ 增强版分析完成!")
        print("📊 统计信息:")
        print(f"  - 总类别数: {analysis_stats['total_categories']}")
        print(f"  - 有证据的类别: {analysis_stats['categories_with_evidence']}")
        print(f"  - 检测到风险的类别: {analysis_stats['categories_with_risks']}")
        print(f"  - 平均置信度: {analysis_stats.get('average_confidence', 0.0):.3f}")

        return enhanced_results, analysis_stats

    def convert_to_main_pipe_format(
        self, enhanced_results: dict[str, EnhancedReviewResult]
    ) -> dict[str, Any]:
        """将增强版结果转换为main_pipe.py兼容的格式

        Args:
            enhanced_results: 增强版分析结果

        Returns:
            main_pipe.py格式的结果
        """
        converted_results = {}

        # 映射类别到main_pipe格式
        category_mapping = {
            "consulting_company_info": "sensitive_inquiry",
            "selling_user_info": "sensitive_inquiry",
            "negative_news": "sensitive_inquiry",
            "major_complaints": "sensitive_inquiry",
            "request_contact_information": "sensitive_reply",
            "spam_messages": "sensitive_reply",
        }

        # 初始化结果结构
        converted_results = {
            "sensitive_inquiry": [],
            "sensitive_reply": [],
            "key_contact": {"hit_rule": False, "values": [], "matched_ids": []},
            "government_inquiry": [],
            "internal_system": {"hit_rule": False, "values": [], "matched_ids": []},
        }

        # 转换每个类别的结果
        for category, result in enhanced_results.items():
            if not result.hit_rule:
                continue

            # 构建标准格式的项目
            item = {
                "hit_rule": result.hit_rule,
                "values": result.values,
                "matched_ids": result.matched_ids,
                "confidence_score": result.confidence_score,
                "category": category,
            }

            # 根据映射添加到对应的类别
            target_category = category_mapping.get(category)
            if target_category == "sensitive_inquiry":
                converted_results["sensitive_inquiry"].append(item)
            elif target_category == "sensitive_reply":
                converted_results["sensitive_reply"].append(item)
            elif category == "consulting_company_info":
                # 特殊处理：公司信息查询可能也属于key_contact
                if any(
                    "address" in val.lower() or "location" in val.lower()
                    for val in result.values
                ):
                    converted_results["key_contact"] = item

        return converted_results

    def run_enhanced_pipeline(
        self, messages: list[dict], caseId: str = "", max_workers: int = 6
    ) -> dict[str, Any]:
        """运行完整的增强版管道，模拟main_pipe.py的接口

        Args:
            messages: 消息列表
            caseId: 案例ID
            max_workers: 最大并行工作线程数

        Returns:
            与main_pipe.py兼容的结果格式
        """
        print(f"🚀 开始处理案例: {caseId}")
        print(f"📝 消息数量: {len(messages)}")

        start_time = time.time()

        try:
            # 1. 执行增强版检索和分析
            enhanced_results, analysis_stats = self.enhanced_retrieve_and_analyze(
                original_messages=messages, max_workers=max_workers
            )

            # 2. 转换为main_pipe格式
            print("\n🔄 转换结果格式...")
            converted_results = self.convert_to_main_pipe_format(enhanced_results)

            # 3. 构建最终结果
            final_result = {
                "id": caseId,
                "review_res": converted_results,
                "analysis_stats": analysis_stats,
                "processing_time": time.time() - start_time,
            }

            print(f"\n✅ 案例 {caseId} 处理完成")
            print(f"⏱️  处理时间: {final_result['processing_time']:.2f} 秒")
            print(f"🎯 检测到风险类别: {analysis_stats['categories_with_risks']}")

            return final_result

        except Exception as e:
            print(f"❌ 处理案例 {caseId} 时出错: {e}")
            import traceback

            traceback.print_exc()

            # 返回空结果
            return {
                "id": caseId,
                "review_res": {
                    "sensitive_inquiry": [],
                    "sensitive_reply": [],
                    "key_contact": {"hit_rule": False, "values": [], "matched_ids": []},
                    "government_inquiry": [],
                    "internal_system": {
                        "hit_rule": False,
                        "values": [],
                        "matched_ids": [],
                    },
                },
                "error": str(e),
                "processing_time": time.time() - start_time,
            }


if __name__ == "__main__":
    # 测试代码
    from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore

    # 初始化知识库
    knowledge_base = EnhancedRetrievalCore()

    # 初始化增强版聚合器
    aggregator = EnhancedRetrievalAggregator(knowledge_base)

    # 测试消息
    test_messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "What is the company registered address?",
            "caseId": "test001",
        },
        {
            "id": 2,
            "type": "AGENT",
            "msg": "Our company is registered at 123 Main Street, New York",
            "caseId": "test001",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "Can you provide your email address?",
            "caseId": "test002",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "I want to sell user data for bitcoin",
            "caseId": "test003",
        },
    ]

    # 先插入测试数据
    print("📝 插入测试数据...")
    stats = knowledge_base.insert_documents_enhanced(test_messages)
    print(f"插入统计: {stats}")

    # 等待数据被索引
    import time

    time.sleep(3)

    # 运行增强版管道
    result = aggregator.run_enhanced_pipeline(test_messages, "test_case_001")

    print("\n" + "=" * 50)
    print("最终结果:")
    print("=" * 50)
    import json

    print(json.dumps(result, indent=2, ensure_ascii=False))
