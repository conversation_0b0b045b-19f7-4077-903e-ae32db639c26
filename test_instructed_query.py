#!/usr/bin/env python3
"""
测试带指令的query检索功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dc_ai_red_line_review.main_pipe import BasicPipeline


def test_query_instruction_methods():
    """测试query指令处理方法"""
    print("=" * 60)
    print("测试query指令处理方法")
    print("=" * 60)
    
    try:
        pipeline = BasicPipeline()
        
        # 测试任务描述获取
        categories = [
            "consulting_company_info",
            "selling_user_info", 
            "negative_news",
            "major_complaints",
            "request_contact_information",
            "spam_messages",
            "sensitive_inquiry",
            "sensitive_reply"
        ]
        
        print("📋 各类别的任务描述:")
        for category in categories:
            task_desc = pipeline._get_category_task_description(category)
            print(f"\n🔸 {category}:")
            print(f"   {task_desc}")
        
        # 测试指令格式化
        print("\n" + "=" * 60)
        print("测试指令格式化")
        print("=" * 60)
        
        test_queries = [
            "company information",
            "personal data",
            "negative news"
        ]
        
        for category in categories[:3]:  # 只测试前3个类别
            task_desc = pipeline._get_category_task_description(category)
            print(f"\n📂 类别: {category}")
            
            for query in test_queries[:1]:  # 每个类别只测试1个query
                detailed_query = pipeline._get_detailed_instruct(task_desc, query)
                print(f"\n原始query: {query}")
                print(f"带指令query:")
                print(f"---")
                print(detailed_query)
                print(f"---")
        
        print("\n✅ query指令处理方法测试成功")
        return True
        
    except Exception as e:
        print(f"❌ query指令处理方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_instructed_query_format():
    """测试指令query的格式"""
    print("\n" + "=" * 60)
    print("测试指令query的格式")
    print("=" * 60)
    
    try:
        pipeline = BasicPipeline()
        
        # 测试不同语言的query
        test_cases = [
            ("consulting_company_info", "company information"),
            ("consulting_company_info", "информация о компании"),
            ("sensitive_inquiry", "money laundering"),
            ("sensitive_inquiry", "отмывание денег"),
        ]
        
        for category, query in test_cases:
            task_desc = pipeline._get_category_task_description(category)
            detailed_query = pipeline._get_detailed_instruct(task_desc, query)
            
            print(f"\n📋 类别: {category}")
            print(f"原始query: {query}")
            print(f"完整指令query:")
            print("=" * 40)
            print(detailed_query)
            print("=" * 40)
            
            # 验证格式
            assert "Instruct:" in detailed_query, "Missing 'Instruct:' prefix"
            assert "Query:" in detailed_query, "Missing 'Query:' prefix"
            assert query in detailed_query, "Original query not found in detailed query"
        
        print("\n✅ 指令query格式测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 指令query格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("开始测试带指令的query检索功能...")
    
    # 运行测试
    test1 = test_query_instruction_methods()
    test2 = test_instructed_query_format()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"query指令处理方法测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"指令query格式测试: {'✅ 通过' if test2 else '❌ 失败'}")
    
    if all([test1, test2]):
        print("\n🎉 所有测试通过！带指令的query检索功能正常")
        print("\n📝 功能说明:")
        print("  - 每个类别都有专门的任务描述")
        print("  - query会被格式化为 'Instruct: [任务描述]\\nQuery: [原始query]'")
        print("  - 支持英文和俄语query的指令处理")
    else:
        print("\n⚠️  部分测试失败，请检查实现")
