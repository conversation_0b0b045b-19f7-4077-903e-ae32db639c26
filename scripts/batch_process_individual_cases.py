#!/usr/bin/env python3
"""批量处理individual_cases目录下的案例文件
按照demo.py的方式处理每个案例，并将结果保存为CSV格式
"""

import csv
import json

# import os
import time
from datetime import datetime
from pathlib import Path
from typing import Any

# from pprint import pprint
# import asyncio
from dc_ai_red_line_review import BasicPipeline


def load_case_file(file_path: str) -> dict[str, Any]:
    """加载单个案例文件"""
    try:
        with open(file_path, encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载文件失败 {file_path}: {e}")
        return None


def process_single_case(
    case_data: dict[str, Any], pipeline: BasicPipeline
) -> dict[str, Any]:
    """处理单个案例"""
    case_id = case_data.get("caseId", "unknown")
    messages = case_data.get("messages", [])

    try:
        print(f"🔄 正在处理案例: {case_id}")
        print(f"   消息数量: {len(messages)}")

        # 运行管道处理
        result = pipeline.run(messages=messages, caseId=case_id)
        # result =  asyncio.run(pipeline.run(messages=messages, caseId=case_id))

        print(f"✅ 案例 {case_id} 处理完成")
        return {
            "case_id": case_id,
            "status": "success",
            "result": result,
            "message_count": len(messages),
            "processed_at": datetime.now().isoformat(),
        }

    except Exception as e:
        print(f"❌ 案例 {case_id} 处理失败: {e}")
        return {
            "case_id": case_id,
            "status": "error",
            "error": str(e),
            "message_count": len(messages),
            "processed_at": datetime.now().isoformat(),
        }


def convert_result_to_json_string(result: dict[str, Any]) -> str:
    """将处理结果转换为JSON字符串"""
    try:
        return json.dumps(result, ensure_ascii=False, separators=(",", ":"))
    except Exception as e:
        return f"JSON转换错误: {str(e)}"


def save_results_to_csv(results: list[dict[str, Any]], output_file: str):
    """将结果保存为CSV格式 - 简化版本，直接保存JSON字符串"""
    if not results:
        print("⚠️ 没有结果可保存")
        return

    # 定义CSV的字段
    fieldnames = ["case_id", "status", "message_count", "processed_at", "result_json", "error"]
    
    csv_rows = []
    for result in results:
        row = {
            "case_id": result["case_id"],
            "status": result["status"],
            "message_count": result.get("message_count", 0),
            "processed_at": result["processed_at"],
            "result_json": "",
            "error": ""
        }

        if result["status"] == "success" and "result" in result:
            # 直接将整个result转换为JSON字符串保存到result_json列
            row["result_json"] = convert_result_to_json_string(result["result"])
        elif result["status"] == "error":
            row["error"] = result.get("error", "")

        csv_rows.append(row)

    # 写入CSV文件
    with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_rows)

    print(f"📊 结果已保存到: {output_file}")
    print(f"   总记录数: {len(csv_rows)}")
    print(f"   字段数: {len(fieldnames)}")


def main():
    """主函数"""
    print("🚀 开始批量处理 individual_cases 目录")
    print("=" * 60)

    # 设置路径
    project_root = Path(__file__).parent.parent
    cases_dir = project_root / "data" / "individual_cases"
    results_dir = project_root / "results"

    # 确保结果目录存在
    results_dir.mkdir(exist_ok=True)

    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_output_file = results_dir / f"individual_cases_results_{timestamp}.json"
    csv_output_file = results_dir / f"individual_cases_results_{timestamp}.csv"

    # 检查输入目录
    if not cases_dir.exists():
        print(f"❌ 输入目录不存在: {cases_dir}")
        return

    # 获取所有JSON文件
    json_files = list(cases_dir.glob("*.json"))
    if not json_files:
        print(f"❌ 在 {cases_dir} 中没有找到JSON文件")
        return

    print(f"📁 找到 {len(json_files)} 个案例文件")

    # 初始化管道
    print("🔧 初始化处理管道...")
    pipeline = BasicPipeline()

    # 处理所有案例
    results = []
    start_time = time.time()

    try:
        for i, json_file in enumerate(json_files, 1):
            print(f"\n📋 进度: {i}/{len(json_files)} - {json_file.name}")

            # 加载案例数据
            case_data = load_case_file(str(json_file))
            if case_data is None:
                results.append(
                    {
                        "case_id": json_file.stem,
                        "status": "error",
                        "error": "Failed to load file",
                        "message_count": 0,
                        "processed_at": datetime.now().isoformat(),
                    }
                )
                continue

            # 处理单个案例
            result = process_single_case(case_data, pipeline)
            results.append(result)

            # 可选: 添加延迟以避免过载
            # time.sleep(0.1)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断处理")
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")

    # 计算统计信息
    end_time = time.time()
    total_time = end_time - start_time
    successful_cases = sum(1 for r in results if r["status"] == "success")
    failed_cases = len(results) - successful_cases

    print("\n📊 处理完成统计:")
    print(f"   总处理时间: {total_time:.2f} 秒")
    print(f"   成功案例: {successful_cases}")
    print(f"   失败案例: {failed_cases}")
    print(f"   平均处理时间: {total_time / len(results):.2f} 秒/案例")

    # 保存结果
    print("\n💾 保存结果...")

    # 保存JSON格式
    with open(json_output_file, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"📄 JSON结果已保存到: {json_output_file}")

    # 保存CSV格式
    save_results_to_csv(results, str(csv_output_file))

    print("\n🎉 批量处理完成!")


if __name__ == "__main__":
    main()
