import json
import os
import time

import httpx
from dotenv import load_dotenv
from openai import OpenAI
from pydantic import BaseModel, ValidationError

from dc_ai_red_line_review.core import CoreComponents
from dc_ai_red_line_review.retrieval_core import RetrievalCore
from dc_ai_red_line_review.utils import (
    Cho<PERSON><PERSON><PERSON>extChunker,
    get_logger,
    get_token_count,
    timing_decorator,
)

load_dotenv(override=True)


# 统一的token限制配置
CHUNK_SIZE_TOKENS = 8_000  # 分块大小限制
OVERLAP_TOKENS = 1_000  # 分块重叠大小
MAX_SINGLE_MESSAGE_TOKENS = 7_000  # 单条消息最大token数（小于分块限制）


# Pydantic model for message validation
class MessageItem(BaseModel):
    id: int  # change from str to int to accept numeric IDs
    type: str
    msg: str


class BasicPipeline:
    def __init__(self):
        self.logger = get_logger(module_name="red_line_review")

        if os.environ.get("PROMPT_SOURCE") == "local":
            with open(os.path.join(os.environ["PROMPT_PATH"], "prompt.json")) as file:
                self.prompt_dict = json.load(file)
        else:
            self.prompt_dict = json.loads(os.environ["PROMPT_DICT"])

        self.logger.info("Initializing pipeline components")

        self.model_client = OpenAI(
            base_url=os.environ["QWQ_BASE_URL"],
            api_key=os.environ["QWQ_API_KEY"],
            http_client=httpx.Client(verify=False, timeout=60.0),
        )

        self.core_components = CoreComponents(
            model_client=self.model_client, prompt_dict=self.prompt_dict
        )

        # Initialize Chonkie text chunker
        self.text_chunker = ChonkieTextChunker(
            chunk_size=CHUNK_SIZE_TOKENS, chunk_overlap=OVERLAP_TOKENS
        )
        self.logger.info("Initialized Chonkie text chunker")

        # Initialize RetrievalCore for advanced chunking and vector storage
        self.retrieval_core = RetrievalCore(
            split_by="function",
            split_length=CHUNK_SIZE_TOKENS,
            split_overlap=OVERLAP_TOKENS,
        )
        self.logger.info("Initialized RetrievalCore for vector storage")

    def _truncate_message_content(
        self, msg_content: str, max_tokens: int
    ) -> tuple[str, bool]:
        """智能截断消息内容到指定token限制.

        Args:
            msg_content: 原始消息内容
            max_tokens: 最大允许的token数

        Returns:
            tuple: (截断后的内容, 是否发生了截断)
        """
        # 首先检查是否需要截断
        current_tokens = get_token_count(msg_content)
        if current_tokens <= max_tokens:
            return msg_content, False

        self.logger.warning(
            f"Message content exceeds {max_tokens} tokens ({current_tokens}), truncating..."
        )

        # 使用保守的字符/token比例进行初步截断
        # 3 chars per token 是保守估计，确保截断后不会超限
        target_chars = max_tokens * 3

        # 进行智能截断，尝试在句子边界处截断
        truncated_content = msg_content[:target_chars]

        # 寻找最近的句子结束符
        sentence_endings = ["。", "！", "？", ".", "!", "?", "\n"]
        best_cut_pos = target_chars

        # 在最后200个字符内寻找合适的截断点
        search_start = max(0, target_chars - 200)
        for ending in sentence_endings:
            pos = truncated_content.rfind(ending, search_start)
            if pos > search_start:
                best_cut_pos = pos + 1
                break

        # 应用截断
        final_content = msg_content[:best_cut_pos].strip()

        # 验证截断后的token数，如果还是超限则进一步截断
        final_tokens = get_token_count(final_content)
        iteration = 0
        while final_tokens > max_tokens and iteration < 5:
            # 进一步减少字符数，每次减少20%
            reduction_factor = 0.8
            new_target = int(len(final_content) * reduction_factor)
            final_content = final_content[:new_target].strip()
            final_tokens = get_token_count(final_content)
            iteration += 1
            self.logger.debug(
                f"Iteration {iteration}: reduced to {final_tokens} tokens"
            )

        self.logger.info(
            f"Message truncated: {current_tokens} -> {final_tokens} tokens"
        )
        return final_content, True

    def _split_messages_using_retrieval_core(
        self, messages: list, case_id: str
    ) -> tuple[list, int]:
        """使用RetrievalCore进行分块处理.

        Args:
            messages: 消息列表
            case_id: 案例ID

        Returns:
            tuple: (分块文档列表, 分块数量)
        """
        try:
            self.logger.info(
                f"Using RetrievalCore for chunking {len(messages)} messages"
            )

            # 使用RetrievalCore的分块方法
            chunk_docs = self.retrieval_core._get_splitter_res(case_id, messages)
            chunk_count = len(chunk_docs)

            self.logger.info(f"RetrievalCore chunking result: {chunk_count} chunks")

            # 打印每个分块的详细信息
            for i, doc in enumerate(chunk_docs):
                content_length = len(doc.content)
                estimated_tokens = get_token_count(doc.content)
                self.logger.info(
                    f"  Chunk {i + 1}: {content_length:,} chars, ~{estimated_tokens:,} tokens"
                )

            return chunk_docs, chunk_count

        except Exception as e:
            self.logger.error(f"RetrievalCore chunking failed: {e}")
            raise ValueError(f"RetrievalCore chunking failed: {e}")

    def _process_large_chunk_count_with_vector_storage(
        self, chunk_docs: list, case_id: str
    ) -> dict:
        """处理大量分块的情况，先插入向量引擎然后进行后续处理.

        Args:
            chunk_docs: 分块文档列表
            case_id: 案例ID

        Returns:
            dict: 处理结果
        """
        self.logger.info(
            f"Processing {len(chunk_docs)} chunks with vector storage for case {case_id}"
        )

        try:
            # 将所有分块插入到向量引擎
            self.logger.info("Inserting chunks into vector storage...")
            self.retrieval_core.insert_documents(chunk_docs)
            self.logger.info("Successfully inserted all chunks into vector storage")

            # TODO: 在这里添加后续处理逻辑
            # 用户可以在这里实现：
            # 1. 基于向量检索的智能分析
            # 2. 分层处理策略
            # 3. 增量处理方案
            # 4. 其他自定义逻辑

            return self._placeholder_large_chunk_processing(chunk_docs, case_id)

        except Exception as e:
            self.logger.error(f"Vector storage processing failed: {e}")
            raise ValueError(f"Vector storage processing failed: {e}")

    def _placeholder_large_chunk_processing(
        self, chunk_docs: list, case_id: str
    ) -> dict:
        """大量分块处理的占位函数，用户可以在这里实现自定义逻辑.

        Args:
            chunk_docs: 分块文档列表
            case_id: 案例ID

        Returns:
            dict: 处理结果
        """
        # TODO: 用户在这里实现自定义的大量分块处理逻辑
        # 例如：
        # - 基于向量检索的智能摘要
        # - 分批处理策略
        # - 重要性排序处理
        # - 增量分析方案

        self.logger.warning(
            f"Using placeholder processing for {len(chunk_docs)} chunks in case {case_id}. "
            "Please implement custom logic in _placeholder_large_chunk_processing method."
        )

        # 暂时返回空结果，用户可以根据需要修改
        return {
            "key_contact": {},
            "sensitive_inquiry": [],
            "sensitive_reply": [],
            "government_inquiry": [],
            "internal_system": {},
        }

    def _split_content_to_token_limit(
        self, content_to_split: str, limit: int
    ) -> tuple[list[str], list[int]]:
        """简化的内容分块方法，使用Chonkie进行分块并打印token信息."""
        if not content_to_split:
            return [], []

        print(f"\n🔪 开始分块: {len(content_to_split):,} 字符")

        try:
            # 使用Chonkie进行分块
            chunks, chunk_tokens = self.text_chunker.chunk_text(content_to_split)

            print("📊 分块结果:")
            print(f"  - 分块数量: {len(chunks)}")
            print(f"  - 各块token数: {chunk_tokens}")
            print(f"  - 总token数: {sum(chunk_tokens):,}")

            # 打印每个分块的详细信息
            for i, (chunk, tokens) in enumerate(zip(chunks, chunk_tokens)):
                print(f"  📄 分块 {i + 1}: {len(chunk):,} 字符, {tokens:,} tokens")

            return chunks, chunk_tokens

        except Exception as e:
            print(f"❌ Chonkie分块失败: {e}")
            # 简单fallback：使用get_token_count计算
            estimated_tokens = get_token_count(content_to_split)
            print(f"📄 Fallback: 单个分块, {estimated_tokens:,} tokens")
            return [content_to_split], [estimated_tokens]

    def _split_messages_by_conversation(
        self, messages: list, token_limit: int
    ) -> tuple[list[list], list[int]]:
        """Split messages into chunks by conversation turns while respecting token limits.

        This method preserves conversation context by keeping complete message exchanges
        together and provides intelligent overlap based on conversation flow.

        Args:
            messages: List of message dictionaries with 'id', 'type', 'msg' fields
            token_limit: Maximum tokens per chunk

        Returns:
            tuple: (list of message chunks, list of token counts for each chunk)
        """
        if not messages:
            return [], []

        print(f"\n🔪 开始消息分块: {len(messages)} 条消息")

        try:
            # 使用Chonkie的消息分块功能
            message_chunks, chunk_tokens = self.text_chunker.chunk_messages(
                messages, token_limit
            )

            print("📊 消息分块结果:")
            print(f"  - 分块数量: {len(message_chunks)}")
            print(f"  - 各块token数: {chunk_tokens}")
            print(f"  - 总token数: {sum(chunk_tokens):,}")

            # 打印每个分块的详细信息
            for i, (msg_chunk, tokens) in enumerate(zip(message_chunks, chunk_tokens)):
                first_id = msg_chunk[0]["id"] if msg_chunk else "N/A"
                last_id = msg_chunk[-1]["id"] if msg_chunk else "N/A"
                print(
                    f"  📄 分块 {i + 1}: {len(msg_chunk)} 消息 (ID {first_id}-{last_id}), {tokens:,} tokens"
                )

            return message_chunks, chunk_tokens

        except Exception as e:
            print(f"❌ Chonkie消息分块失败: {e}")
            # 简单fallback：所有消息作为一个分块
            total_tokens = sum(
                get_token_count(f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>")
                for msg in messages
            )
            print(
                f"📄 Fallback: 单个分块, {len(messages)} 消息, {total_tokens:,} tokens"
            )
            return [messages], [total_tokens]

    def _format_message_chunk_to_content(self, message_chunk: list) -> str:
        """Convert a chunk of messages back to formatted content string."""
        if not message_chunk:
            return ""

        formatted_parts = [
            f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>"
            for msg in message_chunk
        ]
        return "\n".join(formatted_parts)

    def _merge_review_results(self, list_of_chunk_results: list[dict]) -> dict:
        """Merges results from multiple chunks. For each category, results are collected and deduplicated."""
        # Early returns for simple cases
        if not list_of_chunk_results:
            return {}
        if len(list_of_chunk_results) == 1:
            return list_of_chunk_results[0]  # No merge needed if only one chunk

        self.logger.info(
            f"Merging review results from {len(list_of_chunk_results)} chunks"
        )

        # Extract all unique categories from valid dictionaries
        all_categories = set().union(
            *(res.keys() for res in list_of_chunk_results if isinstance(res, dict))
        )

        # Build merged results dictionary with deduplication for each category
        merged_results = {}
        for category in all_categories:
            # Collect all results for this category
            category_results = [
                res[category]
                for res in list_of_chunk_results
                if isinstance(res, dict) and category in res
            ]

            # Deduplicate results based on their content
            if category_results:
                if isinstance(category_results[0], list):
                    # If it's a list of items, merge and deduplicate
                    all_items = []
                    for result_list in category_results:
                        all_items.extend(result_list)
                    # Deduplicate based on string representation of the item
                    seen = set()
                    deduplicated_items = []
                    for item in all_items:
                        item_str = (
                            str(sorted(item.items()))
                            if isinstance(item, dict)
                            else str(item)
                        )
                        if item_str not in seen:
                            seen.add(item_str)
                            deduplicated_items.append(item)
                    merged_results[category] = deduplicated_items
                elif isinstance(category_results[0], dict):
                    # If it's a single dict, merge values and deduplicate
                    merged_dict = {"values": []}
                    all_values = []
                    for result_dict in category_results:
                        if "values" in result_dict:
                            all_values.extend(result_dict["values"])
                    # Deduplicate values
                    merged_dict["values"] = list(set(all_values)) if all_values else []
                    # Copy other fields from the first result
                    for key, value in category_results[0].items():
                        if key != "values":
                            merged_dict[key] = value
                    merged_results[category] = merged_dict
                else:
                    # For other types, just take the first result
                    merged_results[category] = category_results[0]

        return merged_results

    @timing_decorator
    def _validate_and_sort_messages(self, messages, caseId):
        """Validate and sort messages by 'id' using Pydantic. Raises ValueError if invalid."""
        if not isinstance(messages, list):
            raise ValueError(
                f"caseId {caseId}: 'messages' must be a list, got {type(messages)}"
            )
        if not messages:
            self.logger.warning(f"caseId {caseId}: empty messages list")
            return []
        try:
            msgs = [MessageItem(**msg) for msg in messages]
        except ValidationError as e:
            raise ValueError(f"caseId {caseId}: invalid messages data - {e}")
        # sort by id and convert to dict
        return [m.model_dump() for m in sorted(msgs, key=lambda x: x.id)]

    def _process_values_and_matched_ids(self, item_with_values, messages):
        """Process a single item containing values, add matched_ids and deduplicate.

        Only keep values that can be found in the original messages.

        Args:
            item_with_values: Dictionary containing values field
            messages: Message list
        """
        # First deduplicate values while maintaining order
        unique_values = list(dict.fromkeys(item_with_values["values"]))

        # Only keep values that can be matched in original messages
        validated_values = []
        validated_matched_ids = []

        for value in unique_values:
            ids = [msg["id"] for msg in messages if value in msg["msg"]]
            if ids:  # Only keep values that have matches in original text
                validated_values.append(value)
                # Deduplicate ids for each value
                unique_ids = list(dict.fromkeys(ids))  # Order-preserving deduplication
                validated_matched_ids.append(unique_ids)
                self.logger.debug(
                    f"Validated value: '{value}' -> matched IDs: {unique_ids}"
                )
            else:
                self.logger.warning(
                    f"Discarded unverifiable value: '{value}' (not found in original messages)"
                )

        # Update with only validated values
        item_with_values["values"] = validated_values
        item_with_values["matched_ids"] = validated_matched_ids

        # Update hit_rule based on whether we have any validated values
        if not validated_values:
            item_with_values["hit_rule"] = False
            self.logger.info("No validated values found, setting hit_rule to False")

    def attach_matched_ids(self, review_res, messages):
        """Add matched_ids field to each review item with values, structured as a 2D array. Add matched_ids: [] even if values is empty.

        Deduplicate values and matched_ids to avoid duplicate content and IDs.
        """
        for category, items in review_res.items():
            if isinstance(items, list):
                for item in items:
                    if "values" in item:
                        self._process_values_and_matched_ids(item, messages)
            elif isinstance(items, dict) and "values" in items:
                self._process_values_and_matched_ids(items, messages)
        return review_res

    @timing_decorator
    def run(self, messages: list, caseId: str = ""):
        """Process and analyze messages, handling chunking and language detection."""
        self.logger.info(
            f"Processing caseId: {caseId} with {len(messages) if isinstance(messages, list) else 0} messages"
        )

        # Step 1: Validate and prepare messages
        try:
            msgs = self._validate_and_sort_messages(messages, caseId)
            if not msgs:
                return {"id": caseId, "review_res": {}}

            # Format messages into model-ready format
            formatted_parts = [
                f"<|im_start|>{m['type']}\n{m['msg']}<|im_end|>" for m in msgs
            ]
            full_content = "\n".join(formatted_parts)

            # 添加详细的内容长度日志
            self.logger.info(f"Content statistics for caseId {caseId}:")
            self.logger.info(f"  - Total messages: {len(msgs)}")
            self.logger.info(f"  - Total characters: {len(full_content):,}")
            self.logger.info(
                f"  - Average chars per message: {len(full_content) // len(msgs) if msgs else 0}"
            )

            # 检查并截断超长消息
            truncated_count = 0
            for i, msg in enumerate(msgs):
                original_msg_len = len(msg["msg"])

                # 对消息内容进行截断检查
                truncated_content, was_truncated = self._truncate_message_content(
                    msg["msg"], MAX_SINGLE_MESSAGE_TOKENS
                )

                if was_truncated:
                    truncated_count += 1
                    msg["msg"] = truncated_content  # 更新消息内容
                    self.logger.warning(
                        f"Message {i + 1} (ID: {msg['id']}) was truncated: "
                        f"{original_msg_len:,} -> {len(truncated_content):,} characters"
                    )

                # 重新计算截断后的token数量
                formatted_msg = f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>"
                msg_tokens = get_token_count(formatted_msg)

                self.logger.debug(
                    f"  Message {i + 1} (ID: {msg['id']}): {len(msg['msg']):,} chars, ~{msg_tokens:,} tokens"
                    + (" [TRUNCATED]" if was_truncated else "")
                )

            if truncated_count > 0:
                self.logger.info(
                    f"Total {truncated_count} messages were truncated to fit token limits"
                )
                # 重新生成full_content，因为消息内容可能被截断了
                formatted_parts = [
                    f"<|im_start|>{m['type']}\n{m['msg']}<|im_end|>" for m in msgs
                ]
                full_content = "\n".join(formatted_parts)

            self.logger.debug(f"Content prepared for caseId {caseId}: \n{full_content}")

        except ValueError as e:
            raise ValueError(f"Message validation failed: {e}")

        # Step 2: Handle content size and chunking using RetrievalCore
        try:
            # Calculate tokens once for the full content
            total_tokens = get_token_count(full_content)
            self.logger.info(f"Content tokens: {total_tokens:,} (estimated)")
            self.logger.info(f"Chunk size limit: {CHUNK_SIZE_TOKENS:,} tokens")
            self.logger.info(
                f"Character to token ratio: {len(full_content) / total_tokens:.2f} chars/token"
            )

            # 使用RetrievalCore进行分块处理
            chunk_docs, chunk_count = self._split_messages_using_retrieval_core(
                msgs, caseId
            )

            self.logger.info(f"RetrievalCore generated {chunk_count} chunks")

            # 根据分块数量决定处理策略
            if chunk_count < 10:
                self.logger.info(
                    f"Chunk count ({chunk_count}) < 10, using normal processing"
                )

                # 转换为原有格式进行正常处理
                chunks = []
                chunk_tokens = []

                for i, doc in enumerate(chunk_docs):
                    chunk_content = doc.content
                    chunks.append(chunk_content)

                    # 计算实际token数
                    actual_tokens = get_token_count(chunk_content)
                    chunk_tokens.append(actual_tokens)

                    self.logger.info(
                        f"  Chunk {i + 1}: {len(chunk_content):,} chars, ~{actual_tokens:,} tokens"
                    )

            else:
                self.logger.warning(
                    f"Chunk count ({chunk_count}) >= 10, using vector storage processing"
                )

                # 使用向量存储处理大量分块
                vector_result = self._process_large_chunk_count_with_vector_storage(
                    chunk_docs, caseId
                )

                # 直接返回向量处理结果，跳过后续的正常处理流程
                vector_result = self.attach_matched_ids(vector_result, msgs)
                return {"id": caseId, "review_res": vector_result}

        except Exception as e:
            self.logger.error(f"RetrievalCore processing failed: {e}")
            self.logger.warning("Falling back to original chunking method")

            # Fallback to original chunking method
            total_tokens = get_token_count(full_content)
            if total_tokens > CHUNK_SIZE_TOKENS:
                message_chunks, chunk_tokens = self._split_messages_by_conversation(
                    msgs, CHUNK_SIZE_TOKENS
                )
                chunks = []
                for msg_chunk in message_chunks:
                    chunk_content = self._format_message_chunk_to_content(msg_chunk)
                    chunks.append(chunk_content)
            else:
                chunks = [full_content]
                chunk_tokens = [total_tokens]

        # Step 3: Process all chunks sequentially
        self.logger.info(f"Starting sequential processing of {len(chunks)} chunks")

        results = []
        for i, (chunk, tokens) in enumerate(zip(chunks, chunk_tokens)):
            self.logger.info(
                f"Processing chunk {i + 1}/{len(chunks)} (~{tokens} tokens)"
            )

            chunk_result = self.run_sync(chunk, tokens)

            self.logger.info(
                f"Completed chunk {i + 1}/{len(chunks)} (~{tokens} tokens)"
            )

            results.append(chunk_result)

        # Step 4: Merge results and detect language
        merged_results = self._merge_review_results(results)
        merged_results = self.attach_matched_ids(merged_results, msgs)

        return {"id": caseId, "review_res": merged_results}

    def run_sync(self, content, content_tokens=None):
        """Run all review tasks sequentially and merge results."""
        result_dict = {}

        # Use pre-calculated token count or calculate once if not provided
        if content_tokens is None:
            content_tokens = get_token_count(content) if content else 0

        self.logger.info(
            f"Starting sequential execution of review tasks with {content_tokens:,} tokens"
        )

        # Execute tasks sequentially
        try:
            # Task 1: Key contact review
            self.logger.info("Executing task: key_contact")
            start_time = time.time()
            result_dict["key_contact"] = self.core_components.key_contact_review(
                content=content, risk_keywords=self.prompt_dict["key_contact"]
            )
            self.logger.info(
                f"Task key_contact completed in {time.time() - start_time:.2f}s"
            )

            # Task 2: Unified sensitive content review (main API call)
            self.logger.info("Executing task: unified_sensitive_review")
            start_time = time.time()
            unified_result = self.core_components.unified_all_review_sync(
                content, content_tokens
            )
            self.logger.info(
                f"Task unified_sensitive_review completed in {time.time() - start_time:.2f}s"
            )

            # Task 3: Government inquiry review
            self.logger.info("Executing task: government_inquiry")
            start_time = time.time()
            result_dict["government_inquiry"] = (
                self.core_components.government_inquiry_review(
                    content, self.prompt_dict["government_inquiry"]
                )
            )
            self.logger.info(
                f"Task government_inquiry completed in {time.time() - start_time:.2f}s"
            )

            # Task 4: Internal system review
            self.logger.info("Executing task: internal_system")
            start_time = time.time()
            result_dict["internal_system"] = self.core_components.key_contact_review(
                content=content,
                risk_keywords=self.prompt_dict["internal_system"],
            )
            self.logger.info(
                f"Task internal_system completed in {time.time() - start_time:.2f}s"
            )

        except Exception as e:
            self.logger.error(f"Error during sequential task execution: {e}")
            # Ensure we have some result structure even if tasks fail
            if "key_contact" not in result_dict:
                result_dict["key_contact"] = {}
            if "government_inquiry" not in result_dict:
                result_dict["government_inquiry"] = []
            if "internal_system" not in result_dict:
                result_dict["internal_system"] = {}
            unified_result = {"sensitive_inquiry": [], "sensitive_reply": []}

        self.logger.info("All review tasks completed sequentially")

        # Process unified review results and split back to original format
        if isinstance(unified_result, dict):
            # Extract sensitive_inquiry and sensitive_reply from unified result
            if "sensitive_inquiry" in unified_result:
                result_dict["sensitive_inquiry"] = unified_result["sensitive_inquiry"]
            else:
                result_dict["sensitive_inquiry"] = []

            if "sensitive_reply" in unified_result:
                result_dict["sensitive_reply"] = unified_result["sensitive_reply"]
            else:
                result_dict["sensitive_reply"] = []
        else:
            # Fallback if unified result format is unexpected
            self.logger.warning("Unified result format unexpected, using empty results")
            result_dict["sensitive_inquiry"] = []
            result_dict["sensitive_reply"] = []

        return result_dict


if __name__ == "__main__":
    # Use context manager to ensure proper resource cleanup
    # with BasicPipeline() as pipeline:
    pipeline = BasicPipeline()
    caseId = "10066130"
    messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "customer",
        },
        {
            "id": 2,
            "type": "USER",
            "msg": "customer support",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "support",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "KuCoin Pay Menu",
        },
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support. I understand your frustration and rest assured that I will help you through out this process. I would appreciate if you could give me 2-3 minutes to understand your issue and address the best possible solution as soon as possible. Thank you for your patience.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line while I transfer you to our relevant representatives for further assistance.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。",
        },
        {
            "id": 9,
            "type": "USER",
            "msg": "好的",
        },
        {
            "id": 10,
            "type": "AGENT",
            "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？",
        },
        {
            "id": 11,
            "type": "USER",
            "msg": "是的",
        },
        {
            "id": 12,
            "type": "USER",
            "msg": "報稅機關要求我提供",
        },
        {
            "id": 13,
            "type": "AGENT",
            "msg": "好的請稍等",
        },
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mah&eacute; , Republic of Seychelles",
        },
        {
            "id": 15,
            "type": "USER",
            "msg": "謝謝你",
        },
        {
            "id": 16,
            "type": "AGENT",
            "msg": "不客气",
        },
        {
            "id": 17,
            "type": "AGENT",
            "msg": "請問還有其他可以幫助您的嗎？",
        },
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話，如果您有任何其他問題，請隨時與我們聯繫。我們提供7*24小時客戶支援！😊",
        },
    ]

    from pprint import pprint

    pprint(pipeline.run(messages=messages, caseId=caseId))
