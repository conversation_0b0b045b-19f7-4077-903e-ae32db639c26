import json
import os
import time

import httpx
from dotenv import load_dotenv
from openai import OpenAI
from pydantic import BaseModel, ValidationError

from dc_ai_red_line_review.core import CoreComponents
from dc_ai_red_line_review.retrieval_core import RetrievalCore
from dc_ai_red_line_review.utils import (
    get_logger,
    get_token_count,
    timing_decorator,
)

load_dotenv(override=True)


# 统一的token限制配置
CHUNK_SIZE_TOKENS = 8_000  # 分块大小限制
OVERLAP_TOKENS = 1_000  # 分块重叠大小


# Pydantic model for message validation
class MessageItem(BaseModel):
    id: int  # change from str to int to accept numeric IDs
    type: str
    msg: str


class BasicPipeline:
    def __init__(self):
        self.logger = get_logger(module_name="red_line_review")

        if os.environ.get("PROMPT_SOURCE") == "local":
            with open(os.path.join(os.environ["PROMPT_PATH"], "prompt.json")) as file:
                self.prompt_dict = json.load(file)
        else:
            self.prompt_dict = json.loads(os.environ["PROMPT_DICT"])

        self.logger.info("Initializing pipeline components")

        self.model_client = OpenAI(
            base_url=os.environ["QWQ_BASE_URL"],
            api_key=os.environ["QWQ_API_KEY"],
            http_client=httpx.Client(verify=False, timeout=60.0),
        )

        self.core_components = CoreComponents(
            model_client=self.model_client, prompt_dict=self.prompt_dict
        )

        # Initialize RetrievalCore for advanced chunking and vector storage
        self.retrieval_core = RetrievalCore(
            split_length=CHUNK_SIZE_TOKENS,
            split_overlap=OVERLAP_TOKENS,
        )
        self.logger.info("Initialized RetrievalCore for vector storage")

    def _load_query_config(self) -> dict:
        """从配置文件加载检索query配置.

        Returns:
            dict: 类别查询配置字典
        """
        try:
            config_path = os.path.join(os.path.dirname(__file__), "query_config.json")
            with open(config_path, encoding="utf-8") as f:
                config = json.load(f)

            category_queries = config.get("category_queries", {})
            self.logger.info(
                f"Loaded query config for {len(category_queries)} categories"
            )

            # 打印每个类别的query数量
            for category, queries in category_queries.items():
                self.logger.debug(f"Category {category}: {len(queries)} queries")

            return category_queries

        except FileNotFoundError:
            self.logger.error("Query config file not found, using empty config")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse query config file: {e}")
            return {}
        except Exception as e:
            self.logger.error(f"Error loading query config: {e}")
            return {}

    def _split_messages_using_retrieval_core(
        self, messages: list, case_id: str
    ) -> tuple[list, int]:
        """使用RetrievalCore进行分块处理.

        Args:
            messages: 消息列表
            case_id: 案例ID

        Returns:
            tuple: (分块文档列表, 分块数量)
        """
        try:
            self.logger.info(
                f"Using RetrievalCore for chunking {len(messages)} messages"
            )

            # 使用RetrievalCore的分块方法
            chunk_docs = self.retrieval_core._get_splitter_res(case_id, messages)
            chunk_count = len(chunk_docs)

            self.logger.info(f"RetrievalCore chunking result: {chunk_count} chunks")

            # 打印每个分块的详细信息
            for i, doc in enumerate(chunk_docs):
                content_length = len(doc.content)
                estimated_tokens = get_token_count(doc.content)
                self.logger.info(
                    f"  Chunk {i + 1}: {content_length:,} chars, ~{estimated_tokens:,} tokens"
                )

            return chunk_docs, chunk_count

        except Exception as e:
            self.logger.error(f"RetrievalCore chunking failed: {e}")
            raise ValueError(f"RetrievalCore chunking failed: {e}")

    def _process_large_chunk_count_with_vector_storage(
        self, chunk_docs: list, case_id: str
    ) -> dict:
        """处理大量分块的情况，先插入向量引擎然后进行后续处理.

        Args:
            chunk_docs: 分块文档列表
            case_id: 案例ID

        Returns:
            dict: 处理结果
        """
        self.logger.info(
            f"Processing {len(chunk_docs)} chunks with vector storage for case {case_id}"
        )

        try:
            # 将所有分块插入到向量引擎
            self.logger.info("Inserting chunks into vector storage...")
            self.retrieval_core.insert_documents(chunk_docs)
            self.logger.info("Successfully inserted all chunks into vector storage")

            # TODO: 在这里添加后续处理逻辑
            # 用户可以在这里实现：
            # 1. 基于向量检索的智能分析
            # 2. 分层处理策略
            # 3. 增量处理方案
            # 4. 其他自定义逻辑

            return self._placeholder_large_chunk_processing(chunk_docs, case_id)

        except Exception as e:
            self.logger.error(f"Vector storage processing failed: {e}")
            raise ValueError(f"Vector storage processing failed: {e}")

    def _placeholder_large_chunk_processing(
        self, chunk_docs: list, case_id: str
    ) -> dict:
        """大量分块处理：多query检索 + 基于文档ID去重 + 分类审核.

        Args:
            chunk_docs: 分块文档列表
            case_id: 案例ID

        Returns:
            dict: 处理结果
        """
        self.logger.info(
            f"Starting multi-query retrieval processing for {len(chunk_docs)} chunks"
        )

        # 1. 从配置文件加载检索query
        category_queries = self._load_query_config()

        # 2. 对每个类别进行检索并去重
        category_contents = {}
        all_retrieved_doc_ids = set()  # 全局去重集合

        for category, queries in category_queries.items():
            self.logger.info(
                f"Processing category: {category} with {len(queries)} queries"
            )

            category_doc_ids = set()  # 当前类别的文档ID集合
            category_documents = []  # 当前类别的文档列表

            for query in queries:
                try:
                    # 使用RetrievalCore进行检索
                    retrieved_docs = self.retrieval_core.search(
                        query=query,
                        filters={"caseId": case_id},
                        top_k=5,  # 每个query最多检索5个文档
                    )

                    # 基于文档ID去重
                    for doc in retrieved_docs:
                        doc_id = doc.id
                        if doc_id not in category_doc_ids:
                            category_doc_ids.add(doc_id)
                            category_documents.append(doc)
                            all_retrieved_doc_ids.add(doc_id)

                    self.logger.debug(
                        f"Query '{query}' retrieved {len(retrieved_docs)} docs"
                    )

                except Exception as e:
                    self.logger.warning(f"Search failed for query '{query}': {e}")
                    continue

            category_contents[category] = category_documents
            self.logger.info(
                f"Category {category}: {len(category_documents)} unique documents"
            )

        self.logger.info(
            f"Total unique documents retrieved: {len(all_retrieved_doc_ids)}"
        )

        # 3. 对每个类别的检索内容进行审核
        results = {}
        for category, documents in category_contents.items():
            if not documents:
                # 如果没有检索到相关文档，返回空结果
                if category in ["key_contact", "internal_system"]:
                    results[category] = {}
                else:
                    results[category] = []
                continue

            self.logger.info(
                f"Reviewing {len(documents)} documents for category: {category}"
            )

            category_results = []
            for i, doc in enumerate(documents):
                try:
                    self.logger.debug(
                        f"Reviewing document {i + 1}/{len(documents)} for {category}"
                    )

                    # 使用run_sync对单个文档内容进行审核
                    doc_result = self.run_sync(doc.content)

                    # 只提取当前类别的结果
                    if category in doc_result:
                        category_results.append(doc_result[category])

                except Exception as e:
                    self.logger.error(f"Failed to review document for {category}: {e}")
                    continue

            # 4. 合并当前类别的所有结果
            if category_results:
                merged_category_result = self._merge_category_results(
                    category_results, category
                )
                results[category] = merged_category_result
            else:
                # 没有有效结果时的默认值
                if category in ["key_contact", "internal_system"]:
                    results[category] = {}
                else:
                    results[category] = []

            self.logger.info(f"Category {category} processing completed")

        self.logger.info("Multi-query retrieval processing completed")
        return results

    def _merge_category_results(self, category_results: list, category: str):
        """合并单个类别的多个审核结果.

        Args:
            category_results: 单个类别的多个审核结果列表
            category: 类别名称

        Returns:
            合并后的类别结果
        """
        if not category_results:
            return {} if category in ["key_contact", "internal_system"] else []

        if len(category_results) == 1:
            return category_results[0]

        self.logger.debug(
            f"Merging {len(category_results)} results for category: {category}"
        )

        # 根据类别类型进行不同的合并策略
        if category in ["key_contact", "internal_system"]:
            # 字典类型：合并values并去重
            merged_result = {"values": [], "hit_rule": False}
            all_values = []

            for result in category_results:
                if isinstance(result, dict) and "values" in result:
                    all_values.extend(result["values"])
                    if result.get("hit_rule", False):
                        merged_result["hit_rule"] = True

            # 去重values
            merged_result["values"] = list(dict.fromkeys(all_values))
            return merged_result

        else:
            # 列表类型：合并所有项目并去重
            all_items = []
            for result in category_results:
                if isinstance(result, list):
                    all_items.extend(result)

            # 基于字符串表示去重
            seen = set()
            deduplicated_items = []
            for item in all_items:
                item_str = (
                    str(sorted(item.items())) if isinstance(item, dict) else str(item)
                )
                if item_str not in seen:
                    seen.add(item_str)
                    deduplicated_items.append(item)

            return deduplicated_items

    def _merge_review_results(self, list_of_chunk_results: list[dict]) -> dict:
        """Merges results from multiple chunks. For each category, results are collected and deduplicated."""
        # Early returns for simple cases
        if not list_of_chunk_results:
            return {}
        if len(list_of_chunk_results) == 1:
            return list_of_chunk_results[0]  # No merge needed if only one chunk

        self.logger.info(
            f"Merging review results from {len(list_of_chunk_results)} chunks"
        )

        # Extract all unique categories from valid dictionaries
        all_categories = set().union(
            *(res.keys() for res in list_of_chunk_results if isinstance(res, dict))
        )

        # Build merged results dictionary with deduplication for each category
        merged_results = {}
        for category in all_categories:
            # Collect all results for this category
            category_results = [
                res[category]
                for res in list_of_chunk_results
                if isinstance(res, dict) and category in res
            ]

            # Deduplicate results based on their content
            if category_results:
                if isinstance(category_results[0], list):
                    # If it's a list of items, merge and deduplicate
                    all_items = []
                    for result_list in category_results:
                        all_items.extend(result_list)
                    # Deduplicate based on string representation of the item
                    seen = set()
                    deduplicated_items = []
                    for item in all_items:
                        item_str = (
                            str(sorted(item.items()))
                            if isinstance(item, dict)
                            else str(item)
                        )
                        if item_str not in seen:
                            seen.add(item_str)
                            deduplicated_items.append(item)
                    merged_results[category] = deduplicated_items
                elif isinstance(category_results[0], dict):
                    # If it's a single dict, merge values and deduplicate
                    merged_dict = {"values": []}
                    all_values = []
                    for result_dict in category_results:
                        if "values" in result_dict:
                            all_values.extend(result_dict["values"])
                    # Deduplicate values
                    merged_dict["values"] = list(set(all_values)) if all_values else []
                    # Copy other fields from the first result
                    for key, value in category_results[0].items():
                        if key != "values":
                            merged_dict[key] = value
                    merged_results[category] = merged_dict
                else:
                    # For other types, just take the first result
                    merged_results[category] = category_results[0]

        return merged_results

    @timing_decorator
    def _validate_and_sort_messages(self, messages, caseId):
        """Validate and sort messages by 'id' using Pydantic. Raises ValueError if invalid."""
        if not isinstance(messages, list):
            raise ValueError(
                f"caseId {caseId}: 'messages' must be a list, got {type(messages)}"
            )
        if not messages:
            self.logger.warning(f"caseId {caseId}: empty messages list")
            return []
        try:
            msgs = [MessageItem(**msg) for msg in messages]
        except ValidationError as e:
            raise ValueError(f"caseId {caseId}: invalid messages data - {e}")
        # sort by id and convert to dict
        return [m.model_dump() for m in sorted(msgs, key=lambda x: x.id)]

    def _process_values_and_matched_ids(self, item_with_values, messages):
        """Process a single item containing values, add matched_ids and deduplicate.

        Only keep values that can be found in the original messages.

        Args:
            item_with_values: Dictionary containing values field
            messages: Message list
        """
        # First deduplicate values while maintaining order
        unique_values = list(dict.fromkeys(item_with_values["values"]))

        # Only keep values that can be matched in original messages
        validated_values = []
        validated_matched_ids = []

        for value in unique_values:
            ids = [msg["id"] for msg in messages if value in msg["msg"]]
            if ids:  # Only keep values that have matches in original text
                validated_values.append(value)
                # Deduplicate ids for each value
                unique_ids = list(dict.fromkeys(ids))  # Order-preserving deduplication
                validated_matched_ids.append(unique_ids)
                self.logger.debug(
                    f"Validated value: '{value}' -> matched IDs: {unique_ids}"
                )
            else:
                self.logger.warning(
                    f"Discarded unverifiable value: '{value}' (not found in original messages)"
                )

        # Update with only validated values
        item_with_values["values"] = validated_values
        item_with_values["matched_ids"] = validated_matched_ids

        # Update hit_rule based on whether we have any validated values
        if not validated_values:
            item_with_values["hit_rule"] = False
            self.logger.info("No validated values found, setting hit_rule to False")

    def attach_matched_ids(self, review_res, messages):
        """Add matched_ids field to each review item with values, structured as a 2D array. Add matched_ids: [] even if values is empty.

        Deduplicate values and matched_ids to avoid duplicate content and IDs.
        """
        for category, items in review_res.items():
            if isinstance(items, list):
                for item in items:
                    if "values" in item:
                        self._process_values_and_matched_ids(item, messages)
            elif isinstance(items, dict) and "values" in items:
                self._process_values_and_matched_ids(items, messages)
        return review_res

    @timing_decorator
    def run(self, messages: list, caseId: str = ""):
        """Process and analyze messages, handling chunking and language detection."""
        self.logger.info(
            f"Processing caseId: {caseId} with {len(messages) if isinstance(messages, list) else 0} messages"
        )

        # Step 1: Validate and prepare messages
        try:
            msgs = self._validate_and_sort_messages(messages, caseId)
            if not msgs:
                return {"id": caseId, "review_res": {}}

            self.logger.info(
                f"Content prepared for caseId {caseId} with {len(msgs)} messages"
            )

        except ValueError as e:
            raise ValueError(f"Message validation failed: {e}")

        # Step 2: Handle content size and chunking using RetrievalCore
        try:
            # 使用RetrievalCore进行分块处理
            chunk_docs, chunk_count = self._split_messages_using_retrieval_core(
                msgs, caseId
            )

            self.logger.info(f"RetrievalCore generated {chunk_count} chunks")

            # 根据分块数量决定处理策略
            if chunk_count <= 10:
                self.logger.info(
                    f"Chunk count ({chunk_count}) <= 10, using normal sequential processing"
                )

                # 直接处理每个分块，不需要复杂的token计算
                results = []
                for i, doc in enumerate(chunk_docs):
                    self.logger.info(f"Processing chunk {i + 1}/{chunk_count}")

                    chunk_result = self.run_sync(doc.content)
                    results.append(chunk_result)

                    self.logger.info(f"Completed chunk {i + 1}/{chunk_count}")

                # 合并结果并返回
                merged_results = self._merge_review_results(results)
                merged_results = self.attach_matched_ids(merged_results, msgs)
                return {"id": caseId, "review_res": merged_results}

            else:
                self.logger.warning(
                    f"Chunk count ({chunk_count}) >= 10, using vector storage processing"
                )

                # 使用向量存储处理大量分块
                vector_result = self._process_large_chunk_count_with_vector_storage(
                    chunk_docs, caseId
                )

                # 直接返回向量处理结果，跳过后续的正常处理流程
                vector_result = self.attach_matched_ids(vector_result, msgs)
                return {"id": caseId, "review_res": vector_result}

        except Exception as e:
            self.logger.error(f"RetrievalCore processing failed: {e}")
            self.logger.error("No fallback available, RetrievalCore is required")
            raise ValueError(
                f"RetrievalCore processing failed and no fallback available: {e}"
            )

    def run_sync(self, content):
        """Run all review tasks sequentially and merge results."""
        result_dict = {}

        self.logger.info("Starting sequential execution of review tasks")

        # Execute tasks sequentially
        try:
            # Task 1: Key contact review
            self.logger.info("Executing task: key_contact")
            start_time = time.time()
            result_dict["key_contact"] = self.core_components.key_contact_review(
                content=content, risk_keywords=self.prompt_dict["key_contact"]
            )
            self.logger.info(
                f"Task key_contact completed in {time.time() - start_time:.2f}s"
            )

            # Task 2: Unified sensitive content review (main API call)
            self.logger.info("Executing task: unified_sensitive_review")
            start_time = time.time()
            unified_result = self.core_components.unified_all_review_sync(content)
            self.logger.info(
                f"Task unified_sensitive_review completed in {time.time() - start_time:.2f}s"
            )

            # Task 3: Government inquiry review
            self.logger.info("Executing task: government_inquiry")
            start_time = time.time()
            result_dict["government_inquiry"] = (
                self.core_components.government_inquiry_review(
                    content, self.prompt_dict["government_inquiry"]
                )
            )
            self.logger.info(
                f"Task government_inquiry completed in {time.time() - start_time:.2f}s"
            )

            # Task 4: Internal system review
            self.logger.info("Executing task: internal_system")
            start_time = time.time()
            result_dict["internal_system"] = self.core_components.key_contact_review(
                content=content,
                risk_keywords=self.prompt_dict["internal_system"],
            )
            self.logger.info(
                f"Task internal_system completed in {time.time() - start_time:.2f}s"
            )

        except Exception as e:
            self.logger.error(f"Error during sequential task execution: {e}")
            # Ensure we have some result structure even if tasks fail
            if "key_contact" not in result_dict:
                result_dict["key_contact"] = {}
            if "government_inquiry" not in result_dict:
                result_dict["government_inquiry"] = []
            if "internal_system" not in result_dict:
                result_dict["internal_system"] = {}
            unified_result = {"sensitive_inquiry": [], "sensitive_reply": []}

        self.logger.info("All review tasks completed sequentially")

        # Process unified review results and split back to original format
        if isinstance(unified_result, dict):
            # Extract sensitive_inquiry and sensitive_reply from unified result
            if "sensitive_inquiry" in unified_result:
                result_dict["sensitive_inquiry"] = unified_result["sensitive_inquiry"]
            else:
                result_dict["sensitive_inquiry"] = []

            if "sensitive_reply" in unified_result:
                result_dict["sensitive_reply"] = unified_result["sensitive_reply"]
            else:
                result_dict["sensitive_reply"] = []
        else:
            # Fallback if unified result format is unexpected
            self.logger.warning("Unified result format unexpected, using empty results")
            result_dict["sensitive_inquiry"] = []
            result_dict["sensitive_reply"] = []

        return result_dict


if __name__ == "__main__":
    # Use context manager to ensure proper resource cleanup
    # with BasicPipeline() as pipeline:
    pipeline = BasicPipeline()
    caseId = "10066130"
    messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "customer",
        },
        {
            "id": 2,
            "type": "USER",
            "msg": "customer support",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "support",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "KuCoin Pay Menu",
        },
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support. I understand your frustration and rest assured that I will help you through out this process. I would appreciate if you could give me 2-3 minutes to understand your issue and address the best possible solution as soon as possible. Thank you for your patience.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line while I transfer you to our relevant representatives for further assistance.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。",
        },
        {
            "id": 9,
            "type": "USER",
            "msg": "好的",
        },
        {
            "id": 10,
            "type": "AGENT",
            "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？",
        },
        {
            "id": 11,
            "type": "USER",
            "msg": "是的",
        },
        {
            "id": 12,
            "type": "USER",
            "msg": "報稅機關要求我提供",
        },
        {
            "id": 13,
            "type": "AGENT",
            "msg": "好的請稍等",
        },
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mah&eacute; , Republic of Seychelles",
        },
        {
            "id": 15,
            "type": "USER",
            "msg": "謝謝你",
        },
        {
            "id": 16,
            "type": "AGENT",
            "msg": "不客气",
        },
        {
            "id": 17,
            "type": "AGENT",
            "msg": "請問還有其他可以幫助您的嗎？",
        },
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話，如果您有任何其他問題，請隨時與我們聯繫。我們提供7*24小時客戶支援！😊",
        },
    ]

    from pprint import pprint

    pprint(pipeline.run(messages=messages, caseId=caseId))
